/**
 * 无障碍服务测试脚本
 * 功能：测试无障碍服务功能和权限
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== 无障碍服务脚本开始执行 ===");

// 测试1: 获取当前应用包名
try {
    var currentApp = currentPackage();
    console.log("当前应用包名: " + currentApp);
    toast("当前应用: " + currentApp);
    sleep(2000);
} catch (e) {
    console.log("获取当前应用包名失败: " + e.message);
    toast("获取应用包名失败");
}

// 测试2: 检查无障碍服务状态
try {
    var rootNode = getRootInActiveWindow();
    if (rootNode) {
        console.log("无障碍服务正常工作");
        console.log("根节点类名: " + rootNode.className());
        toast("无障碍服务已启用");
        
        // 获取子节点数量
        var childCount = rootNode.getChildCount();
        console.log("根节点子节点数量: " + childCount);
        
        rootNode.recycle();
        sleep(2000);
    } else {
        console.log("无障碍服务可能未正常工作");
        toast("无障碍服务状态异常");
    }
} catch (e) {
    console.log("检查无障碍服务时出错: " + e.message);
    toast("无障碍服务检查失败");
}

// 测试3: 检查屏幕状态
try {
    var isScreenOn = device.isScreenOn();
    console.log("屏幕状态: " + (isScreenOn ? "开启" : "关闭"));
    toast("屏幕状态: " + (isScreenOn ? "开启" : "关闭"));
    sleep(2000);
} catch (e) {
    console.log("检查屏幕状态失败: " + e.message);
}

// 测试4: 获取活动窗口信息
try {
    var activeWindow = getRootInActiveWindow();
    if (activeWindow) {
        var windowInfo = {
            className: activeWindow.className(),
            packageName: activeWindow.packageName(),
            childCount: activeWindow.getChildCount()
        };
        
        console.log("活动窗口信息:");
        console.log("- 类名: " + windowInfo.className);
        console.log("- 包名: " + windowInfo.packageName);
        console.log("- 子节点数: " + windowInfo.childCount);
        
        toast("窗口类名: " + windowInfo.className);
        activeWindow.recycle();
    }
} catch (e) {
    console.log("获取窗口信息失败: " + e.message);
}

console.log("=== 无障碍服务脚本执行完成 ===");
