{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,337,419,516,632,715,782,875,952,1015,1131,1200,1259,1330,1389,1443,1564,1625,1688,1742,1815,1937,2025,2108,2230,2316,2403,2494,2561,2627,2699,2776,2860,2935,3012,3094,3170,3259,3341,3432,3528,3602,3683,3778,3832,3898,3985,4071,4133,4197,4260,4370,4477,4580,4689", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "332,414,511,627,710,777,870,947,1010,1126,1195,1254,1325,1384,1438,1559,1620,1683,1737,1810,1932,2020,2103,2225,2311,2398,2489,2556,2622,2694,2771,2855,2930,3007,3089,3165,3254,3336,3427,3523,3597,3678,3773,3827,3893,3980,4066,4128,4192,4255,4365,4472,4575,4684,4764"}, "to": {"startLines": "2,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3117,3930,4027,4143,4300,4462,4555,4632,4695,4811,4880,4939,5010,5069,5123,5244,5305,5368,5422,5495,5617,5705,5788,5910,5996,6083,6174,6241,6307,6379,6456,6540,6615,6692,6774,6850,6939,7021,7112,7208,7282,7363,7458,7512,7578,7665,7751,7813,7877,7940,8050,8157,8260,8683", "endLines": "7,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "382,3194,4022,4138,4221,4362,4550,4627,4690,4806,4875,4934,5005,5064,5118,5239,5300,5363,5417,5490,5612,5700,5783,5905,5991,6078,6169,6236,6302,6374,6451,6535,6610,6687,6769,6845,6934,7016,7107,7203,7277,7358,7453,7507,7573,7660,7746,7808,7872,7935,8045,8152,8255,8364,8758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,274,353,495,664,746", "endColumns": "73,94,78,141,168,81,77", "endOffsets": "174,269,348,490,659,741,819"}, "to": {"startLines": "46,48,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4226,4367,8604,8763,9088,9257,9339", "endColumns": "73,94,78,141,168,81,77", "endOffsets": "4295,4457,8678,8900,9252,9334,9412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,125", "endOffsets": "159,285"}, "to": {"startLines": "97,98", "startColumns": "4,4", "startOffsets": "8369,8478", "endColumns": "108,125", "endOffsets": "8473,8599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "387,507,610,726,812,917,1036,1116,1193,1285,1379,1474,1568,1663,1757,1853,1948,2040,2132,2213,2319,2424,2522,2630,2736,2844,3017,8905", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "502,605,721,807,912,1031,1111,1188,1280,1374,1469,1563,1658,1752,1848,1943,2035,2127,2208,2314,2419,2517,2625,2731,2839,3012,3112,8982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "36,37,38,39,40,41,42,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3199,3297,3399,3499,3600,3706,3809,8987", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3292,3394,3494,3595,3701,3804,3925,9083"}}]}]}