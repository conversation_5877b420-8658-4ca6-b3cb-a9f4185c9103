1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.hardware.telephony"
12-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
13        android:required="false" />
13-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
15-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
16-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
17-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
18-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
19-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
20-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
21-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
22-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
23-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
24-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
26-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
28-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
29-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
30-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
31-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
32-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
33-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
34-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
35-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
36-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
37-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
38-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
39-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
40-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
41-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
42-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
43-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
44
45    <!-- AutoJs required permissions -->
46    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
46-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-81
46-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-78
47    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
47-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:44:5-45:40
47-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:44:22-79
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
48-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-89
48-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-86
49    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
49-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-48:53
49-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-74
50    <uses-permission android:name="android.permission.GET_TASKS" />
50-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:49:5-68
50-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:49:22-65
51
52    <!-- Scheduled script permissions -->
53    <uses-permission android:name="android.permission.SET_ALARM" />
53-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:52:5-68
53-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:52:22-65
54    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
54-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-74
54-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-71
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:55:5-219:19
63        android:name="com.bm.atool.App"
63-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:65:9-28
64        android:allowBackup="true"
64-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:dataExtractionRules="@xml/data_extraction_rules"
66-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-65
67        android:debuggable="true"
68        android:extractNativeLibs="false"
69        android:fullBackupContent="@xml/backup_rules"
69-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-54
70        android:icon="@mipmap/ic_launcher"
70-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-43
71        android:label="@string/app_name"
71-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-41
72        android:roundIcon="@mipmap/ic_launcher_round"
72-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-54
73        android:supportsRtl="true"
73-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-35
74        android:theme="@style/Theme.AndroidTool"
74-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-49
75        android:usesCleartextTraffic="true" >
75-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-44
76        <activity
76-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:67:9-69:40
77            android:name="com.bm.atool.LoginActivity"
77-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:68:13-42
78            android:exported="false" />
78-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-37
79        <activity
79-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-83:20
80            android:name="com.bm.atool.MainActivity"
80-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-41
81            android:excludeFromRecents="true"
81-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-46
82            android:exported="true"
82-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
83            android:label="@string/app_name"
83-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-45
84            android:launchMode="singleTask"
84-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-44
85            android:taskAffinity=""
85-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
86            android:theme="@style/Theme.AndroidTool" >
86-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-53
87            <intent-filter>
87-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-82:29
88                <action android:name="android.intent.action.MAIN" />
88-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:79:17-69
88-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:79:25-66
89
90                <category android:name="android.intent.category.LAUNCHER" />
90-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:81:17-77
90-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:81:27-74
91            </intent-filter>
92        </activity>
93        <activity
93-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:84:9-86:39
94            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
94-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:85:13-68
95            android:exported="true" />
95-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-36
96
97        <receiver
97-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:88:9-94:20
98            android:name="com.bm.atool.receivers.SimChangedReceiver"
98-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:89:13-57
99            android:exported="true" >
99-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-36
100            <intent-filter>
100-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-93:29
101                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
101-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:92:17-81
101-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:92:25-79
102            </intent-filter>
103        </receiver>
104        <receiver
104-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:95:9-101:20
105            android:name="com.bm.atool.receivers.SmsReceiver"
105-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:95:19-56
106            android:exported="true"
106-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:96:13-36
107            android:permission="android.permission.BROADCAST_SMS" >
107-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-66
108            <intent-filter>
108-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-100:29
109                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
109-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-81
109-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-79
110            </intent-filter>
111        </receiver>
112        <receiver
112-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-116:20
113            android:name="com.bm.atool.receivers.WakeUpReceiver"
113-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-53
114            android:exported="true"
114-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
115            android:process=":watch" >
115-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
116            <intent-filter>
116-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-115:29
117                <action android:name="android.intent.action.USER_PRESENT" />
117-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
117-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
118                <action android:name="android.intent.action.BOOT_COMPLETED" />
118-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
118-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
119                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
119-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
119-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
120                <action android:name="android.intent.action.USER_PRESENT" />
120-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
120-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
121                <action android:name="android.intent.action.MEDIA_MOUNTED" />
121-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
121-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
122                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
122-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-87
122-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-84
123                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
123-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-90
123-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-87
124            </intent-filter>
125        </receiver>
126        <receiver
126-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:117:9-144:20
127            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
127-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:118:13-62
128            android:exported="true"
128-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-36
129            android:process=":watch" >
129-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-37
130
131            <!-- 手机启动 -->
132            <intent-filter>
132-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:122:13-125:29
133                <action android:name="android.intent.action.BOOT_COMPLETED" />
133-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
133-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
134                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
134-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
134-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
135            </intent-filter>
136            <!-- 软件安装卸载 -->
137            <intent-filter>
137-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:127:13-131:29
138                <action android:name="android.intent.action.PACKAGE_ADDED" />
138-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:128:17-77
138-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:128:25-75
139                <action android:name="android.intent.action.PACKAGE_REMOVED" />
139-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-79
139-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-77
140
141                <data android:scheme="package" />
141-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
141-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
142            </intent-filter>
143            <!-- 网络监听 -->
144            <intent-filter>
144-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:133:13-137:29
145                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
145-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
145-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
146                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
146-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:135:17-77
146-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:135:25-75
147                <action android:name="android.net.wifi.STATE_CHANGE" />
147-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-71
147-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-69
148            </intent-filter>
149            <!-- 文件挂载 -->
150            <intent-filter>
150-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:139:13-143:29
151                <action android:name="android.intent.action.MEDIA_EJECT" />
151-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:140:17-75
151-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:140:25-73
152                <action android:name="android.intent.action.MEDIA_MOUNTED" />
152-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
152-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
153
154                <data android:scheme="file" />
154-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
154-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
155            </intent-filter>
156        </receiver>
157        <receiver
157-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:146:9-157:20
158            android:name="com.bm.atool.receivers.ScheduledScriptBootReceiver"
158-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:147:13-66
159            android:enabled="true"
159-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-35
160            android:exported="true" >
160-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-36
161            <intent-filter android:priority="1000" >
161-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-156:29
161-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:150:28-51
162                <action android:name="android.intent.action.BOOT_COMPLETED" />
162-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
162-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
163                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
163-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:152:17-84
163-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:152:25-81
164                <action android:name="android.intent.action.PACKAGE_REPLACED" />
164-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:153:17-81
164-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:153:25-78
165
166                <data android:scheme="package" />
166-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
166-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
167
168                <category android:name="android.intent.category.DEFAULT" />
168-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:155:17-76
168-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:155:27-73
169            </intent-filter>
170        </receiver>
171
172        <!-- 守护进程 watch -->
173        <service
173-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:160:9-165:43
174            android:name="com.bm.atool.service.JobSchedulerService"
174-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:161:13-56
175            android:enabled="true"
175-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-35
176            android:exported="true"
176-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:164:13-36
177            android:permission="android.permission.BIND_JOB_SERVICE"
177-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:162:13-69
178            android:process=":watch_job" />
178-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-41
179        <service
179-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:167:9-172:43
180            android:name="com.bm.atool.service.WatchDogService"
180-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-52
181            android:enabled="true"
181-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-35
182            android:exported="true"
182-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:171:13-36
183            android:foregroundServiceType="mediaPlayback"
183-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-58
184            android:process=":watch_dog" />
184-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:172:13-41
185        <service
185-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:174:9-176:46
186            android:name="com.bm.atool.service.PlayMusicService"
186-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:174:18-58
187            android:foregroundServiceType="mediaPlayback"
187-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:175:13-58
188            android:process=":watch_player" />
188-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:176:13-44
189        <service
189-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:177:9-190:19
190            android:name="com.bm.atool.service.ANTAccessibilityService"
190-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:178:13-60
191            android:enabled="true"
191-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:179:13-35
192            android:exported="true"
192-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-36
193            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
193-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-79
194            android:process=":accessibility" >
194-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-45
195            <intent-filter>
195-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-185:29
196                <action android:name="android.accessibilityservice.AccessibilityService" />
196-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:184:17-92
196-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:184:25-89
197            </intent-filter>
198
199            <meta-data
199-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:54
200                android:name="android.accessibilityservice"
200-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-60
201                android:resource="@xml/allocation" />
201-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:189:17-51
202        </service>
203        <service
203-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:191:9-207:19
204            android:name="com.bm.atool.service.SocketService"
204-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:192:13-50
205            android:exported="false"
205-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:193:13-37
206            android:label="SocketService"
206-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:194:13-42
207            android:permission="android.permission.BIND_VPN_SERVICE"
207-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:196:13-69
208            android:process=":socket" >
208-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:195:13-38
209            <intent-filter>
209-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:197:13-199:29
210                <action android:name="android.net.VpnService" />
210-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:198:17-65
210-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:198:25-62
211            </intent-filter>
212
213            <meta-data
213-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:200:13-202:39
214                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
214-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:201:17-73
215                android:value="true" />
215-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:202:17-37
216
217            <property
217-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:203:13-205:38
218                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
218-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:204:17-76
219                android:value="vpn" />
219-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:205:17-36
220        </service>
221        <service
221-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:209:9-217:19
222            android:name="com.bm.atool.service.NotificationService"
222-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:210:13-56
223            android:exported="true"
223-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:213:13-36
224            android:label="@string/app_name"
224-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:211:13-45
225            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
225-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:212:13-87
226            <intent-filter>
226-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:214:13-216:29
227                <action android:name="android.service.notification.NotificationListenerService" />
227-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:215:17-99
227-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:215:25-96
228            </intent-filter>
229        </service>
230
231        <provider
231-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
232            android:name="androidx.startup.InitializationProvider"
232-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
233            android:authorities="com.bm.atool.androidx-startup"
233-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
234            android:exported="false" >
234-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
235            <meta-data
235-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
236                android:name="androidx.emoji2.text.EmojiCompatInitializer"
236-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
237                android:value="androidx.startup" />
237-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
238            <meta-data
238-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
239                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
239-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
240                android:value="androidx.startup" />
240-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
241            <meta-data
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
242                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
243                android:value="androidx.startup" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
244        </provider>
245
246        <uses-library
246-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
247            android:name="androidx.window.extensions"
247-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
248            android:required="false" />
248-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
249        <uses-library
249-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
250            android:name="androidx.window.sidecar"
250-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
251            android:required="false" />
251-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
252
253        <receiver
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
254            android:name="androidx.profileinstaller.ProfileInstallReceiver"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
255            android:directBootAware="false"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
256            android:enabled="true"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
257            android:exported="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
258            android:permission="android.permission.DUMP" >
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
260                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
263                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
266                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
269                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
270            </intent-filter>
271        </receiver>
272    </application>
273
274</manifest>
