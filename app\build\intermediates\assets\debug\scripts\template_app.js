/**
 * App launch and operation template script
 * Parameters:
 * - packageName: App package name, like "com.android.settings"
 * - activityName: Activity name (optional)
 * - action: Operation type, values: launch, close
 * - waitTime: Wait time after operation, default 2000ms
 */

// Get parameters
var args = engines.myEngine().execArgv;
var packageName = args.packageName;
var activityName = args.activityName;
var action = args.action || "launch";
var waitTime = args.waitTime || 2000;

// Request accessibility permission
auto.waitFor();

// Log function, will send logs to server
function log(message) {
    console.log(message);
    // Execution result will be automatically returned to server via script executor
}

log("开始执行应用操作");

if (!packageName) {
    log("参数错误：需要提供应用包名");
    exit();
}

// Execute different logic based on action type
switch (action.toLowerCase()) {
    case "launch":
        try {
            if (activityName) {
                log("启动应用: " + packageName + ", 活动: " + activityName);
                app.startActivity({
                    packageName: packageName,
                    className: activityName,
                    action: "android.intent.action.MAIN",
                    category: ["android.intent.category.LAUNCHER"],
                    flags: ["android.intent.flag.activity.NEW_TASK"]
                });
            } else {
                log("启动应用: " + packageName);
                launch(packageName);
            }
            
            // Wait for app to launch
            sleep(waitTime);
            
            // Check if app launched successfully
            if (currentPackage() === packageName) {
                log("应用已成功启动");
            } else {
                log("应用可能未成功启动，当前包名: " + currentPackage());
            }
        } catch (e) {
            log("启动应用异常: " + e);
        }
        break;
        
    case "close":
        log("关闭应用: " + packageName);
        try {
            app.openAppSetting(packageName);
            sleep(1000);
            
            // Find and click "Force stop" button
            var forceStop = text("强行停止").findOne(3000) || 
                           text("强制停止").findOne(3000) ||
                           text("Force stop").findOne(3000);
                           
            if (forceStop) {
                forceStop.click();
                sleep(1000);
                
                // Click confirm button
                var confirm = text("确定").findOne(3000) || 
                             text("确认").findOne(3000) || 
                             text("OK").findOne(3000);
                if (confirm) {
                    confirm.click();
                    log("已强制停止应用");
                }
            } else {
                log("未找到强制停止按钮");
            }
        } catch (e) {
            log("关闭应用异常: " + e);
        }
        break;
        
    default:
        log("未知操作类型: " + action);
        break;
}

// Wait specified time
sleep(waitTime);
log("应用操作执行完成"); 