/**
 * Text input template script
 * Parameters:
 * - text: Text to input
 * - index: Input field index, default 0
 * - clearBefore: Whether to clear input field before input, default true
 * - waitTime: Wait time after operation, default 1000ms
 */

// Get parameters
var args = engines.myEngine().execArgv;
var text = args.text || "";
var index = args.index || 0;
var clearBefore = args.clearBefore !== false;
var waitTime = args.waitTime || 1000;

// Request accessibility permission
auto.waitFor();

// Log function, will send logs to server
function log(message) {
    console.log(message);
    // Execution result will be automatically returned to server via script executor
}

log("开始执行文本输入");

// Find input field
var inputField = className("android.widget.EditText").findOnce(index);

if (!inputField) {
    log("未找到输入框，尝试查找所有可编辑控件");
    inputField = className("android.widget.EditText").findOnce();
}

if (inputField) {
    // Get current focus
    inputField.focus();
    
    // If needed, clear input field first
    if (clearBefore) {
        log("清空输入框");
        inputField.setText("");
    }
    
    // Input text
    log("输入文本：" + text);
    inputField.setText(text);
    
    // Wait specified time
    sleep(waitTime);
    log("文本输入完成");
} else {
    log("未找到任何输入框");
}

// Wait specified time
sleep(waitTime);
log("输入操作执行完成"); 