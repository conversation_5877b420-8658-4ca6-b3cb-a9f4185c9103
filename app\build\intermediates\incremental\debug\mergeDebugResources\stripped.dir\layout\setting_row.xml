<RelativeLayout
xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
xmlns:tools="http://schemas.android.com/tools"
android:layout_width="match_parent"
android:layout_height="wrap_content"
android:orientation="vertical"
android:padding="4dp">
<TextView
    android:id="@+id/txtName"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="Android 6.0"
    android:layout_centerVertical="true"
    android:textColor="@color/text_primary" />

<ImageView
    android:id="@+id/imgStatus"
    android:layout_width="36dp"
    android:layout_height="36dp"
    android:layout_alignParentRight="true"
    android:layout_centerVertical="true"
    android:scaleType="centerInside"
    app:srcCompat="@drawable/ic_action_ok"
    android:tint="@color/check_mark_color"
    tools:srcCompat="@drawable/ic_action_ok" />

    <Button
        android:id="@+id/btnGoSetting"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:backgroundTint="@color/accent_color"
        android:maxHeight="32dp"
        android:paddingLeft="6dp"
        android:paddingTop="2dp"
        android:paddingRight="6dp"
        android:paddingBottom="2dp"
        android:textColor="@color/surface_background"
        android:text="Grant" />

</RelativeLayout>
