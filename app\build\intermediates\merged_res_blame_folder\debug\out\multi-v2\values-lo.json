{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,305,420,539,622,688,777,846,905,1000,1065,1123,1188,1249,1309,1415,1476,1536,1594,1665,1784,1870,1952,2065,2140,2216,2306,2373,2439,2508,2582,2661,2734,2811,2880,2950,3035,3110,3203,3296,3370,3439,3533,3585,3652,3736,3820,3882,3946,4009,4108,4200,4295,4387", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "219,300,415,534,617,683,772,841,900,995,1060,1118,1183,1244,1304,1410,1471,1531,1589,1660,1779,1865,1947,2060,2135,2211,2301,2368,2434,2503,2577,2656,2729,2806,2875,2945,3030,3105,3198,3291,3365,3434,3528,3580,3647,3731,3815,3877,3941,4004,4103,4195,4290,4382,4461"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2941,3728,3843,3962,4115,4267,4356,4425,4484,4579,4644,4702,4767,4828,4888,4994,5055,5115,5173,5244,5363,5449,5531,5644,5719,5795,5885,5952,6018,6087,6161,6240,6313,6390,6459,6529,6614,6689,6782,6875,6949,7018,7112,7164,7231,7315,7399,7461,7525,7588,7687,7779,7874,8265", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,80,114,118,82,65,88,68,58,94,64,57,64,60,59,105,60,59,57,70,118,85,81,112,74,75,89,66,65,68,73,78,72,76,68,69,84,74,92,92,73,68,93,51,66,83,83,61,63,62,98,91,94,91,78", "endOffsets": "269,3017,3838,3957,4040,4176,4351,4420,4479,4574,4639,4697,4762,4823,4883,4989,5050,5110,5168,5239,5358,5444,5526,5639,5714,5790,5880,5947,6013,6082,6156,6235,6308,6385,6454,6524,6609,6684,6777,6870,6944,7013,7107,7159,7226,7310,7394,7456,7520,7583,7682,7774,7869,7961,8339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3022,3118,3221,3320,3418,3519,3617,8564", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3113,3216,3315,3413,3514,3612,3723,8660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,479,648,728", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "170,256,336,474,643,723,801"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4045,4181,8185,8344,8665,8834,8914", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "4110,4262,8260,8477,8829,8909,8987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,377,480,593,678,782,893,971,1048,1139,1232,1324,1418,1518,1611,1706,1802,1893,1984,2065,2172,2276,2374,2477,2581,2685,2842,8482", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "372,475,588,673,777,888,966,1043,1134,1227,1319,1413,1513,1606,1701,1797,1888,1979,2060,2167,2271,2369,2472,2576,2680,2837,2936,8559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "7966,8075", "endColumns": "108,109", "endOffsets": "8070,8180"}}]}]}