{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "45,47,98,100,103,104,105", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4181,4318,8520,8674,8996,9165,9252", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "4247,4399,8590,8808,9160,9247,9328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "96,97", "startColumns": "4,4", "startOffsets": "8286,8396", "endColumns": "109,123", "endOffsets": "8391,8515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,449,555,668,752,857,976,1061,1141,1232,1325,1420,1514,1614,1707,1802,1896,1987,2079,2160,2270,2378,2476,2588,2694,2798,2960,8813", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "444,550,663,747,852,971,1056,1136,1227,1320,1415,1509,1609,1702,1797,1891,1982,2074,2155,2265,2373,2471,2583,2689,2793,2955,3056,8890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "2,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3061,3875,3976,4097,4252,4404,4499,4573,4633,4717,4783,4841,4914,4977,5033,5152,5209,5270,5326,5400,5545,5631,5715,5818,5900,5983,6073,6140,6206,6279,6357,6445,6516,6593,6667,6739,6830,6904,6999,7097,7171,7251,7352,7405,7471,7560,7650,7712,7776,7839,7951,8064,8174,8595", "endLines": "6,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "321,3143,3971,4092,4176,4313,4494,4568,4628,4712,4778,4836,4909,4972,5028,5147,5204,5265,5321,5395,5540,5626,5710,5813,5895,5978,6068,6135,6201,6274,6352,6440,6511,6588,6662,6734,6825,6899,6994,7092,7166,7246,7347,7400,7466,7555,7645,7707,7771,7834,7946,8059,8169,8281,8669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "35,36,37,38,39,40,41,102", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3148,3246,3348,3448,3547,3649,3758,8895", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3241,3343,3443,3542,3644,3753,3870,8991"}}]}]}