<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

<!--    app:tabTextColor="@color/teal_700"-->
<!--    app:tabIndicatorColor="@color/teal_700"-->
<!--    app:tabSelectedTextColor="@color/teal_700"-->
    <androidx.viewpager.widget.ViewPager
        android:layout_width="match_parent"
        android:layout_height = "1dp"
        android:layout_weight ="1"
        android:id="@+id/view_pager"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/header_background"
        android:elevation="8dp"
        app:tabGravity="fill"
        app:tabIndicatorGravity="bottom"
        app:tabInlineLabel="true"
        app:tabMode="fixed"
        app:tabIconTint="@null"
        app:tabIndicatorFullWidth="false"
        app:tabTextColor="@color/text_secondary"
        app:tabIndicatorColor="@color/text_primary"
        app:tabIndicatorHeight="4px"
        app:tabPaddingEnd="0dp"
        app:tabPaddingStart="0dp"
        app:tabPaddingTop="0dp"
        app:tabSelectedTextColor="@color/text_primary"
        app:tabTextAppearance="@style/TextAppearance.AppCompat.Medium" />


</LinearLayout>