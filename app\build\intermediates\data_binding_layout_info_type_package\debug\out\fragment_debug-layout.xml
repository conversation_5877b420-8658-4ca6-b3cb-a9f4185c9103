<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_debug" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_debug.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_debug_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="16"/></Target><Target id="@+id/debugTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="45"/></Target><Target id="@+id/btnTestSimpleScript" view="Button"><Expressions/><location startLine="44" startOffset="12" endLine="54" endOffset="50"/></Target><Target id="@+id/btnTestDeviceInfo" view="Button"><Expressions/><location startLine="56" startOffset="12" endLine="66" endOffset="50"/></Target><Target id="@+id/btnTestAdvancedScript" view="Button"><Expressions/><location startLine="68" startOffset="12" endLine="78" endOffset="50"/></Target><Target id="@+id/btnTestAccessibilityScript" view="Button"><Expressions/><location startLine="80" startOffset="12" endLine="90" endOffset="50"/></Target><Target id="@+id/btnTestComplexScript" view="Button"><Expressions/><location startLine="92" startOffset="12" endLine="102" endOffset="50"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="104" startOffset="12" endLine="114" endOffset="51"/></Target><Target id="@+id/tvScriptResult" view="TextView"><Expressions/><location startLine="116" startOffset="12" endLine="126" endOffset="46"/></Target><Target id="@+id/btnStopSocket" view="Button"><Expressions/><location startLine="131" startOffset="4" endLine="142" endOffset="43"/></Target></Targets></Layout>