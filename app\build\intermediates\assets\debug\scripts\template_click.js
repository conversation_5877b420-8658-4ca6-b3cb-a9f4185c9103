/**
 * Click screen template script
 * Parameters:
 * - x: Click X coordinate, if null will use text or desc to find element
 * - y: Click Y coordinate, if null will use text or desc to find element
 * - text: Text of the element to click
 * - desc: Description of the element to click
 * - waitTime: Wait time after operation, default 1000ms
 */

// Get parameters
var args = engines.myEngine().execArgv;
var x = args.x;
var y = args.y;
var text = args.text;
var desc = args.desc;
var waitTime = args.waitTime || 1000;

// Request accessibility permission
auto.waitFor();

// Log function, will send logs to server
function log(message) {
    console.log(message);
    // Execution result will be automatically returned to server via script executor
}

log("开始执行点击操作");

// Decide click method based on parameters
if (x != null && y != null) {
    // Direct coordinate click
    log("执行坐标点击：(" + x + ", " + y + ")");
    click(x, y);
} else if (text) {
    // Find and click by text
    log("查找并点击文本：" + text);
    var target = text(text).findOne(5000);
    if (target) {
        target.click();
        log("已点击文本元素：" + text);
    } else {
        log("未找到文本元素：" + text);
        exit();
    }
} else if (desc) {
    // Find and click by description
    log("查找并点击描述：" + desc);
    var target = desc(desc).findOne(5000);
    if (target) {
        target.click();
        log("已点击描述元素：" + desc);
    } else {
        log("未找到描述元素：" + desc);
        exit();
    }
} else {
    log("参数错误：需要提供坐标(x,y)或文本/描述");
    exit();
}

// Wait specified time
sleep(waitTime);
log("点击操作执行完成"); 