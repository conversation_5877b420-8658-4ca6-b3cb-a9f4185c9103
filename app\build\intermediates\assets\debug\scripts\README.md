# ATool 脚本执行说明

## 无障碍服务启用指南

为了执行自动化脚本，ATool需要启用无障碍服务。请按照以下步骤操作：

1. 打开手机设置
2. 找到"无障碍"或"辅助功能"选项
3. 在服务列表中找到"ATool"
4. 点击并启用该服务
5. 确认安全提示，允许ATool使用无障碍功能

## 脚本执行方式

ATool支持多种脚本执行方式：

### 1. 直接执行脚本
- 在主界面点击"直接执行脚本"按钮
- 系统会自动执行预设的模拟点击脚本

### 2. 通过服务器下发脚本
- 服务器可以下发各种自动化脚本
- 脚本将在设备上自动执行

## 常见问题解决

### 问题1：脚本执行失败
**可能原因：**
- 无障碍服务未启用
- 脚本内容有语法错误
- 设备不支持某些API

**解决方案：**
1. 确保已启用ATool无障碍服务
2. 检查脚本语法是否正确
3. 确认设备Android版本是否满足要求

### 问题2：点击操作无响应
**可能原因：**
- 无障碍服务权限不足
- 应用在后台被系统限制

**解决方案：**
1. 检查无障碍服务是否正常运行
2. 将ATool加入电池优化白名单
3. 允许ATool在后台运行

## 脚本API参考

### 基础API
- `auto.waitFor()` - 等待无障碍服务就绪
- `toast(message)` - 显示Toast消息
- `sleep(ms)` - 暂停执行指定毫秒

### 设备信息
- `device.width` - 屏幕宽度
- `device.height` - 屏幕高度

### 操作API
- `click(x, y)` - 点击指定坐标
- `swipe(x1, y1, x2, y2, duration)` - 滑动操作
- `longClick(x, y, duration)` - 长按操作

## 注意事项

1. 执行脚本时请确保目标应用处于前台
2. 某些操作可能需要额外的权限
3. 避免在脚本中执行过于频繁的操作，以免被系统限制