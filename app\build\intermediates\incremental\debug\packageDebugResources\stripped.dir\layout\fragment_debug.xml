<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/app_background_color"
    android:padding="16dp">

    <TextView
        android:id="@+id/debugTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Debug Options"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        android:padding="8dp"
        android:background="@color/header_background"
        android:elevation="4dp"
        android:layout_alignParentTop="true"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/debugTitle"
        android:layout_above="@id/btnStopSocket"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="AutoJS脚本测试"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:padding="8dp"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnTestSimpleScript"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="测试简单脚本 (Toast)"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnTestDeviceInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="测试设备信息脚本"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnTestAdvancedScript"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="测试高级脚本"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnTestAccessibilityScript"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="测试无障碍服务脚本"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnTestComplexScript"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="测试复杂JavaScript脚本"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/btnStopAllScripts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="#FF5722"
                android:textColor="@color/white"
                android:elevation="4dp"
                android:padding="12dp"
                android:textSize="14sp"
                android:text="停止所有脚本"
                android:layout_marginBottom="16dp"/>

            <TextView
                android:id="@+id/tvScriptResult"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="脚本执行结果将显示在这里..."
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:padding="8dp"
                android:background="#F5F5F5"
                android:maxLines="10"
                android:scrollbars="vertical"/>

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/btnStopSocket"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/button_primary"
        android:textColor="@color/white"
        android:elevation="4dp"
        android:padding="12dp"
        android:textSize="16sp"
        android:text="Stop All Services"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="86dp"/>

</RelativeLayout>