/**
 * 设备信息测试脚本
 * 功能：获取并显示设备详细信息
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== 设备信息脚本开始执行 ===");

// 获取设备基本信息
var deviceInfo = {
    width: device.width,
    height: device.height,
    model: device.model,
    release: device.release,
    sdk: device.sdkInt,
    brand: device.brand || "未知",
    product: device.product || "未知"
};

// 显示设备信息
console.log("设备详细信息:");
console.log("- 屏幕尺寸: " + deviceInfo.width + "x" + deviceInfo.height);
console.log("- 设备型号: " + deviceInfo.model);
console.log("- Android版本: " + deviceInfo.release);
console.log("- SDK版本: " + deviceInfo.sdk);
console.log("- 设备品牌: " + deviceInfo.brand);
console.log("- 产品名称: " + deviceInfo.product);

// 通过Toast显示关键信息
toast("设备: " + deviceInfo.model);
sleep(2000);

toast("屏幕: " + deviceInfo.width + "x" + deviceInfo.height);
sleep(2000);

toast("Android: " + deviceInfo.release);
sleep(2000);

// 检查屏幕方向
var orientation = context.getResources().getConfiguration().orientation;
var orientationText = orientation === 1 ? "竖屏" : "横屏";
console.log("屏幕方向: " + orientationText);
toast("屏幕方向: " + orientationText);

console.log("=== 设备信息脚本执行完成 ===");
