package com.bm.atool.utils;

import android.content.Context;
import android.util.Log;

import com.stardust.autojs.AutoJs;
import com.stardust.autojs.execution.ExecutionConfig;
import com.stardust.autojs.execution.ScriptExecution;
import com.stardust.autojs.execution.ScriptExecutionListener;
import com.stardust.autojs.script.StringScriptSource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import io.socket.client.Socket;

/**
 * AutoJs Script Executor
 * Responsible for receiving, executing AutoJs scripts and returning execution results
 */
public class AutoJsExecutor {
    private static final String TAG = "AutoJsExecutor";
    private static AutoJsExecutor instance;
    private AutoJs autoJs;
    private Context context;
    private ConcurrentHashMap<String, ScriptExecution> runningScripts = new ConcurrentHashMap<>();
    private Socket socket;

    // Private constructor
    private AutoJsExecutor(Context context) {
        this.context = context.getApplicationContext();
        init();
    }

    // Singleton pattern to get instance
    public static synchronized AutoJsExecutor getInstance(Context context) {
        if (instance == null) {
            instance = new AutoJsExecutor(context);
        }
        return instance;
    }

    // Initialize AutoJs
    private void init() {
        try {
            autoJs = AutoJs.getInstance();
            if (autoJs == null) {
                Log.d(TAG, "Initializing AutoJs");
                autoJs = AutoJs.initInstance(context);
            }
            Log.i(TAG, "AutoJs initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "AutoJs initialization failed: " + e.getMessage(), e);
        }
    }

    // Set Socket for returning execution results
    public void setSocket(Socket socket) {
        this.socket = socket;
    }

    /**
     * Execute script
     * @param scriptId Script ID
     * @param script Script content
     * @param params Script parameters
     * @return Execution ID
     */
    public String executeScript(String scriptId, String script, Map<String, Object> params) {
        if (autoJs == null) {
            Log.e(TAG, "AutoJs not initialized");
            sendExecutionResult(scriptId, false, "AutoJs not initialized", null);
            return null;
        }

        String executionId = UUID.randomUUID().toString();
        
        try {
            // Prepare script configuration
            ExecutionConfig config = new ExecutionConfig();
            config.setDelay(0);
            config.setLoggingEnabled(true);
            config.setStopAllOnEnd(false);

            // Prepare script source
            StringScriptSource scriptSource = new StringScriptSource(script);
            
            // Set listener
            ScriptExecutionListener listener = new ScriptExecutionListener() {
                @Override
                public void onStart(ScriptExecution execution) {
                    Log.d(TAG, "Script execution started: " + executionId);
                    sendExecutionResult(scriptId, true, "Script execution started", executionId);
                }

                @Override
                public void onSuccess(ScriptExecution execution, Object result) {
                    Log.d(TAG, "Script execution successful: " + executionId + ", result: " + result);
                    runningScripts.remove(executionId);
                    sendExecutionResult(scriptId, true, "Script execution successful", result);
                }

                @Override
                public void onException(ScriptExecution execution, Throwable e) {
                    Log.e(TAG, "Script execution exception: " + executionId, e);
                    runningScripts.remove(executionId);
                    sendExecutionResult(scriptId, false, "Script execution exception: " + e.getMessage(), null);
                }
            };

            // Execute script
            ScriptExecution execution = autoJs.getScriptEngineService().execute(
                    scriptSource, config, params, listener);
            
            // Save execution instance
            runningScripts.put(executionId, execution);
            return executionId;
            
        } catch (Exception e) {
            Log.e(TAG, "Execute script exception: " + e.getMessage(), e);
            sendExecutionResult(scriptId, false, "Execute script exception: " + e.getMessage(), null);
            return null;
        }
    }

    /**
     * Stop specified script execution
     * @param executionId Execution ID
     */
    public boolean stopScript(String executionId) {
        ScriptExecution execution = runningScripts.get(executionId);
        if (execution != null) {
            execution.getEngine().forceStop();
            runningScripts.remove(executionId);
            Log.d(TAG, "Script stopped: " + executionId);
            return true;
        } else {
            Log.w(TAG, "Cannot find running script: " + executionId);
            return false;
        }
    }

    /**
     * Stop all scripts
     */
    public void stopAllScripts() {
        for (Map.Entry<String, ScriptExecution> entry : runningScripts.entrySet()) {
            try {
                entry.getValue().getEngine().forceStop();
                Log.d(TAG, "Script stopped: " + entry.getKey());
            } catch (Exception e) {
                Log.e(TAG, "Stop script exception: " + e.getMessage(), e);
            }
        }
        runningScripts.clear();
    }

    /**
     * Execute script from assets
     * @param scriptId Script ID
     * @param assetPath Path to script in assets folder
     * @param params Script parameters
     * @return Execution ID
     */
    public String executeScriptFromAssets(String scriptId, String assetPath, Map<String, Object> params) {
        try {
            String script = loadScriptFromAssets(assetPath);
            if (script != null && !script.isEmpty()) {
                return executeScript(scriptId, script, params);
            } else {
                Log.e(TAG, "Failed to load script from assets: " + assetPath);
                sendExecutionResult(scriptId, false, "Failed to load script from assets", null);
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Execute script from assets exception: " + e.getMessage(), e);
            sendExecutionResult(scriptId, false, "Execute script from assets exception: " + e.getMessage(), null);
            return null;
        }
    }

    /**
     * Load script content from assets
     * @param assetPath Path to script in assets folder
     * @return Script content
     */
    private String loadScriptFromAssets(String assetPath) {
        try {
            InputStream inputStream = context.getAssets().open(assetPath);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            StringBuilder stringBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }

            reader.close();
            inputStream.close();

            return stringBuilder.toString();
        } catch (IOException e) {
            Log.e(TAG, "Failed to load script from assets: " + assetPath, e);
            return null;
        }
    }

    /**
     * Send execution result to Socket
     */
    private void sendExecutionResult(String scriptId, boolean success, String message, Object data) {
        if (socket != null && socket.connected()) {
            Map<String, Object> result = new HashMap<>();
            result.put("scriptId", scriptId);
            result.put("success", success);
            result.put("message", message);
            result.put("data", data);

            socket.emit("scriptResult", new com.google.gson.Gson().toJson(result));
        }
    }
}