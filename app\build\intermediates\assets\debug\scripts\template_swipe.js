/**
 * Screen swipe template script
 * Parameters:
 * - x1: Start X coordinate
 * - y1: Start Y coordinate
 * - x2: End X coordinate
 * - y2: End Y coordinate
 * - duration: Swipe duration(ms), default 500ms
 * - waitTime: Wait time after operation, default 1000ms
 * - direction: Preset direction, values: up, down, left, right, if set will ignore coordinates
 */

// Get parameters
var args = engines.myEngine().execArgv;
var x1 = args.x1;
var y1 = args.y1;
var x2 = args.x2;
var y2 = args.y2;
var duration = args.duration || 500;
var waitTime = args.waitTime || 1000;
var direction = args.direction;

// Request accessibility permission
auto.waitFor();

// Log function, will send logs to server
function log(message) {
    console.log(message);
    // Execution result will be automatically returned to server via script executor
}

log("开始执行滑动操作");

// Get screen dimensions
var width = device.width;
var height = device.height;

// If preset direction provided, set corresponding coordinates
if (direction) {
    switch (direction.toLowerCase()) {
        case "up":
            x1 = width / 2;
            y1 = height * 0.7;
            x2 = width / 2;
            y2 = height * 0.3;
            log("上滑操作");
            break;
        case "down":
            x1 = width / 2;
            y1 = height * 0.3;
            x2 = width / 2;
            y2 = height * 0.7;
            log("下滑操作");
            break;
        case "left":
            x1 = width * 0.8;
            y1 = height / 2;
            x2 = width * 0.2;
            y2 = height / 2;
            log("左滑操作");
            break;
        case "right":
            x1 = width * 0.2;
            y1 = height / 2;
            x2 = width * 0.8;
            y2 = height / 2;
            log("右滑操作");
            break;
        default:
            log("未知方向: " + direction + "，使用提供的坐标");
    }
}

// Execute swipe operation
if (x1 != null && y1 != null && x2 != null && y2 != null) {
    log("执行滑动：(" + x1 + "," + y1 + ") -> (" + x2 + "," + y2 + "), 持续时间: " + duration + "ms");
    swipe(x1, y1, x2, y2, duration);
} else {
    log("参数错误：需要提供完整的坐标或有效的方向");
    exit();
}

// Wait specified time
sleep(waitTime);
log("滑动操作执行完成"); 