# ATool AutoJS集成指南

## 概述

本指南详细说明了如何在ATool项目中使用集成的AutoJS功能，包括构建、测试和开发新脚本的完整流程。

## 项目结构

```
app/
├── src/main/
│   ├── java/com/bm/atool/
│   │   ├── ui/DebugFragment.java          # 测试界面
│   │   ├── utils/AutoJsExecutor.java      # 脚本执行引擎
│   │   ├── service/ANTAccessibilityService.java  # 无障碍服务
│   │   └── model/AutoJsRequest.java       # 请求模型
│   ├── assets/scripts/                    # 脚本文件目录
│   │   ├── simple_test.js                 # 简单测试脚本
│   │   ├── device_info_test.js           # 设备信息脚本
│   │   ├── advanced_test.js              # 高级功能脚本
│   │   ├── accessibility_test.js         # 无障碍测试脚本
│   │   ├── complex_test.js               # 复杂JavaScript脚本
│   │   └── README.md                     # 脚本说明文档
│   └── res/layout/fragment_debug.xml     # 测试界面布局
└── build.gradle                          # 依赖配置
```

## 依赖配置

在`app/build.gradle`中已添加以下AutoJS相关依赖：

```gradle
dependencies {
    // AutoJs依赖
    implementation 'com.github.hyb1996:AutoJs-Pro:v1.0.1'
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    implementation 'com.google.code.gson:gson:2.9.0'
}
```

## 权限配置

在`AndroidManifest.xml`中已配置必要权限：

```xml
<!-- AutoJs required permissions -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
<uses-permission android:name="android.permission.GET_TASKS" />
<uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
```

## 核心组件说明

### 1. AutoJsExecutor (脚本执行引擎)

**位置**: `app/src/main/java/com/bm/atool/utils/AutoJsExecutor.java`

**主要功能**:
- 初始化AutoJS环境
- 执行JavaScript脚本
- 管理脚本生命周期
- 处理执行结果回调

**关键方法**:
```java
// 执行脚本字符串
public String executeScript(String scriptId, String script, Map<String, Object> params)

// 从assets加载并执行脚本
public String executeScriptFromAssets(String scriptId, String assetPath, Map<String, Object> params)

// 停止指定脚本
public boolean stopScript(String executionId)

// 停止所有脚本
public void stopAllScripts()
```

### 2. DebugFragment (测试界面)

**位置**: `app/src/main/java/com/bm/atool/ui/DebugFragment.java`

**主要功能**:
- 提供脚本测试界面
- 显示执行结果
- 管理测试按钮和事件

**测试按钮**:
- 测试简单脚本 (Toast)
- 测试设备信息脚本
- 测试高级脚本
- 测试无障碍服务脚本
- 测试复杂JavaScript脚本
- 停止所有脚本

### 3. ANTAccessibilityService (无障碍服务)

**位置**: `app/src/main/java/com/bm/atool/service/ANTAccessibilityService.java`

**主要功能**:
- 提供无障碍服务支持
- 处理无障碍事件
- 支持AutoJS的无障碍API

## 使用步骤

### 1. 构建项目

```bash
# 清理项目
./gradlew clean

# 构建Debug版本
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug
```

### 2. 启用无障碍服务

1. 安装应用后，打开手机设置
2. 找到"无障碍"或"辅助功能"选项
3. 在服务列表中找到"ATool"
4. 点击并启用该服务
5. 确认安全提示，允许ATool使用无障碍功能

### 3. 测试AutoJS功能

1. 打开ATool应用
2. 切换到"DEBUG"标签页
3. 依次点击测试按钮：
   - 先测试"测试简单脚本"确认基础功能
   - 再测试"测试设备信息脚本"验证设备API
   - 然后测试"测试无障碍服务脚本"确认无障碍功能
   - 最后测试"测试高级脚本"和"测试复杂JavaScript脚本"

### 4. 查看测试结果

- **界面显示**: 执行状态会在DebugFragment底部的文本区域显示
- **Toast消息**: 脚本执行过程中会弹出Toast消息
- **Logcat日志**: 使用以下命令查看详细日志：
  ```bash
  adb logcat -s AutoJsExecutor DebugFragment
  ```

## 开发新脚本

### 1. 创建脚本文件

在`app/src/main/assets/scripts/`目录下创建新的`.js`文件：

```javascript
/**
 * 新脚本模板
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== 新脚本开始执行 ===");

// 脚本主体逻辑
try {
    // 你的代码逻辑
    toast("新脚本执行成功");
    
} catch (e) {
    console.log("脚本执行错误: " + e.message);
    toast("脚本执行失败");
}

console.log("=== 新脚本执行完成 ===");
```

### 2. 添加测试按钮

在`DebugFragment.java`中添加新的测试方法：

```java
private void executeNewScript() {
    updateResult("执行新脚本...");
    if (autoJsExecutor != null) {
        String executionId = autoJsExecutor.executeScriptFromAssets(
            "new_script", "scripts/new_script.js", null);
        if (executionId != null) {
            updateResult("新脚本开始执行，ID: " + executionId);
            Toast.makeText(getContext(), "新脚本开始执行", Toast.LENGTH_SHORT).show();
        }
    }
}
```

### 3. 更新布局文件

在`fragment_debug.xml`中添加新的按钮：

```xml
<Button
    android:id="@+id/btnTestNewScript"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="测试新脚本"
    android:backgroundTint="@color/button_primary"
    android:textColor="@color/white" />
```

## 常用AutoJS API

### 基础API
```javascript
auto.waitFor()                    // 等待无障碍服务
toast(message)                    // 显示Toast
sleep(ms)                        // 暂停执行
console.log(message)             // 输出日志
exit()                           // 退出脚本
```

### 设备信息API
```javascript
device.width                     // 屏幕宽度
device.height                    // 屏幕高度
device.model                     // 设备型号
device.release                   // Android版本
device.sdkInt                    // SDK版本
device.isScreenOn()              // 屏幕是否开启
```

### 无障碍API
```javascript
currentPackage()                 // 当前应用包名
getRootInActiveWindow()          // 获取根节点
click(x, y)                      // 点击坐标
swipe(x1, y1, x2, y2, duration) // 滑动手势
```

### 文件操作API
```javascript
files.read(path)                 // 读取文件
files.write(path, content)       // 写入文件
files.exists(path)               // 检查文件存在
```

## 故障排除

### 常见问题及解决方案

1. **AutoJS初始化失败**
   - 检查依赖是否正确添加
   - 确认权限配置完整
   - 重新构建项目

2. **脚本执行失败**
   - 检查无障碍服务是否启用
   - 确认脚本语法正确
   - 查看Logcat错误信息

3. **无障碍功能异常**
   - 重新启用无障碍服务
   - 检查系统权限设置
   - 重启设备

4. **Toast不显示**
   - 检查通知权限
   - 确认脚本正常执行
   - 尝试重启应用

### 调试技巧

1. **使用console.log输出调试信息**
2. **分段测试复杂脚本**
3. **使用try-catch捕获异常**
4. **检查Logcat日志**
5. **使用简单脚本验证环境**

## 性能优化建议

1. **避免长时间运行的脚本**
2. **及时释放无障碍节点资源**
3. **使用适当的sleep间隔**
4. **避免频繁的UI操作**
5. **合理使用内存和CPU资源**

## 安全注意事项

1. **只执行可信的脚本代码**
2. **避免在脚本中处理敏感信息**
3. **定期检查和更新依赖库**
4. **遵循Android权限最小化原则**

## 扩展开发

如需扩展AutoJS功能：

1. **添加自定义API**: 在AutoJsExecutor中扩展功能
2. **集成更多服务**: 结合其他Android服务
3. **优化执行引擎**: 改进脚本执行性能
4. **增强错误处理**: 提供更详细的错误信息

## 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查Logcat日志输出
3. 参考AutoJS官方文档
4. 在项目中提交Issue
