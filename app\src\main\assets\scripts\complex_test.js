/**
 * 复杂JavaScript测试脚本
 * 功能：演示复杂的JavaScript功能和AutoJS集成
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== 复杂JavaScript脚本开始执行 ===");

// 脚本配置对象
var scriptConfig = {
    name: "复杂脚本测试",
    version: "1.0.0",
    author: "AutoJS用户",
    startTime: new Date().toISOString(),
    tasks: [],
    results: {},
    statistics: {
        totalTasks: 0,
        successTasks: 0,
        failedTasks: 0
    }
};

// 工具函数
var Utils = {
    // 日志函数
    log: function(level, message) {
        var timestamp = new Date().toLocaleTimeString();
        var logMessage = "[" + timestamp + "] [" + level + "] " + message;
        console.log(logMessage);
        return logMessage;
    },
    
    // Toast显示函数
    showToast: function(message, duration) {
        duration = duration || "short";
        toast(message);
        if (duration === "long") {
            sleep(3000);
        } else {
            sleep(1500);
        }
    },
    
    // 格式化JSON
    formatJson: function(obj) {
        try {
            return JSON.stringify(obj, null, 2);
        } catch (e) {
            return obj.toString();
        }
    }
};

// 任务管理器
var TaskManager = {
    // 添加任务
    addTask: function(taskName, taskFunction, description) {
        scriptConfig.tasks.push({
            name: taskName,
            function: taskFunction,
            description: description || "",
            id: scriptConfig.tasks.length + 1
        });
        scriptConfig.statistics.totalTasks++;
    },
    
    // 执行单个任务
    executeTask: function(task) {
        try {
            Utils.log("INFO", "开始执行任务: " + task.name);
            var startTime = Date.now();
            
            var result = task.function();
            
            var endTime = Date.now();
            var duration = endTime - startTime;
            
            scriptConfig.results[task.name] = {
                success: true,
                result: result,
                duration: duration,
                timestamp: new Date().toISOString()
            };
            
            scriptConfig.statistics.successTasks++;
            Utils.log("SUCCESS", "任务完成: " + task.name + " (耗时: " + duration + "ms)");
            return result;
        } catch (e) {
            scriptConfig.results[task.name] = {
                success: false,
                error: e.message,
                timestamp: new Date().toISOString()
            };
            
            scriptConfig.statistics.failedTasks++;
            Utils.log("ERROR", "任务失败: " + task.name + " - " + e.message);
            return null;
        }
    },
    
    // 执行所有任务
    executeAllTasks: function() {
        Utils.log("INFO", "开始执行所有任务，共 " + scriptConfig.tasks.length + " 个");
        
        for (var i = 0; i < scriptConfig.tasks.length; i++) {
            var task = scriptConfig.tasks[i];
            this.executeTask(task);
            sleep(1000); // 任务间隔
        }
        
        Utils.log("INFO", "所有任务执行完成");
    }
};

// 定义测试任务
TaskManager.addTask("设备信息收集", function() {
    return {
        screen: {
            width: device.width,
            height: device.height,
            density: context.getResources().getDisplayMetrics().density
        },
        system: {
            model: device.model,
            release: device.release,
            sdk: device.sdkInt,
            brand: device.brand || "未知"
        },
        app: {
            packageName: currentPackage(),
            versionName: context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionName
        }
    };
}, "收集设备和应用信息");

TaskManager.addTask("数学计算测试", function() {
    var numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    var sum = numbers.reduce(function(a, b) { return a + b; }, 0);
    var average = sum / numbers.length;
    var max = Math.max.apply(Math, numbers);
    var min = Math.min.apply(Math, numbers);
    
    return {
        numbers: numbers,
        sum: sum,
        average: average,
        max: max,
        min: min,
        fibonacci: generateFibonacci(10)
    };
    
    function generateFibonacci(n) {
        var fib = [0, 1];
        for (var i = 2; i < n; i++) {
            fib[i] = fib[i-1] + fib[i-2];
        }
        return fib;
    }
}, "执行各种数学计算");

TaskManager.addTask("字符串处理测试", function() {
    var testString = "Hello AutoJS World! 你好 AutoJS 世界！";
    
    return {
        original: testString,
        length: testString.length,
        uppercase: testString.toUpperCase(),
        lowercase: testString.toLowerCase(),
        reversed: testString.split("").reverse().join(""),
        words: testString.split(" "),
        encoded: encodeURIComponent(testString),
        hash: simpleHash(testString)
    };
    
    function simpleHash(str) {
        var hash = 0;
        for (var i = 0; i < str.length; i++) {
            var char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash;
    }
}, "测试字符串操作功能");

TaskManager.addTask("时间处理测试", function() {
    var now = new Date();
    
    return {
        timestamp: now.getTime(),
        iso: now.toISOString(),
        locale: now.toLocaleString(),
        year: now.getFullYear(),
        month: now.getMonth() + 1,
        day: now.getDate(),
        hour: now.getHours(),
        minute: now.getMinutes(),
        second: now.getSeconds(),
        dayOfWeek: ["日", "一", "二", "三", "四", "五", "六"][now.getDay()],
        formatted: formatDate(now)
    };
    
    function formatDate(date) {
        return date.getFullYear() + "年" + 
               (date.getMonth() + 1) + "月" + 
               date.getDate() + "日 " + 
               date.getHours() + ":" + 
               String(date.getMinutes()).padStart(2, '0');
    }
}, "测试时间和日期处理");

// 开始执行任务
Utils.log("INFO", "脚本配置: " + Utils.formatJson(scriptConfig));
Utils.showToast("开始执行复杂脚本测试");

TaskManager.executeAllTasks();

// 生成执行报告
scriptConfig.endTime = new Date().toISOString();
var executionTime = new Date(scriptConfig.endTime).getTime() - new Date(scriptConfig.startTime).getTime();

var report = {
    config: scriptConfig,
    summary: {
        totalTasks: scriptConfig.statistics.totalTasks,
        successTasks: scriptConfig.statistics.successTasks,
        failedTasks: scriptConfig.statistics.failedTasks,
        successRate: (scriptConfig.statistics.successTasks / scriptConfig.statistics.totalTasks * 100).toFixed(2) + "%",
        totalExecutionTime: executionTime + "ms"
    }
};

Utils.log("INFO", "执行报告: " + Utils.formatJson(report.summary));
Utils.showToast("脚本执行完成！成功率: " + report.summary.successRate, "long");

console.log("=== 复杂JavaScript脚本执行完成 ===");
