<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\androidProject2020\android-tool-v2\app\src\main\assets"><file name="scripts/example_scheduled_script.js" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\example_scheduled_script.js"/><file name="scripts/README.md" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\README.md"/><file name="scripts/template_app.js" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\template_app.js"/><file name="scripts/template_click.js" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\template_click.js"/><file name="scripts/template_input.js" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\template_input.js"/><file name="scripts/template_swipe.js" path="E:\androidProject2020\android-tool-v2\app\src\main\assets\scripts\template_swipe.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\androidProject2020\android-tool-v2\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\androidProject2020\android-tool-v2\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>