// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMainBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final Button btnDirectScript;

  @NonNull
  public final Button btnTestScript;

  @NonNull
  public final ImageView imageView;

  @NonNull
  public final ImageView imgStatus;

  @NonNull
  public final LinearLayout layoutIcon;

  @NonNull
  public final LinearLayout layoutUserInfo;

  @NonNull
  public final RecyclerView phoneListView;

  @NonNull
  public final RecyclerView smsListView;

  @NonNull
  public final TextView txtUserName;

  private FragmentMainBinding(@NonNull FrameLayout rootView, @NonNull Button btnDirectScript,
      @NonNull Button btnTestScript, @NonNull ImageView imageView, @NonNull ImageView imgStatus,
      @NonNull LinearLayout layoutIcon, @NonNull LinearLayout layoutUserInfo,
      @NonNull RecyclerView phoneListView, @NonNull RecyclerView smsListView,
      @NonNull TextView txtUserName) {
    this.rootView = rootView;
    this.btnDirectScript = btnDirectScript;
    this.btnTestScript = btnTestScript;
    this.imageView = imageView;
    this.imgStatus = imgStatus;
    this.layoutIcon = layoutIcon;
    this.layoutUserInfo = layoutUserInfo;
    this.phoneListView = phoneListView;
    this.smsListView = smsListView;
    this.txtUserName = txtUserName;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDirectScript;
      Button btnDirectScript = ViewBindings.findChildViewById(rootView, id);
      if (btnDirectScript == null) {
        break missingId;
      }

      id = R.id.btnTestScript;
      Button btnTestScript = ViewBindings.findChildViewById(rootView, id);
      if (btnTestScript == null) {
        break missingId;
      }

      id = R.id.imageView;
      ImageView imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.imgStatus;
      ImageView imgStatus = ViewBindings.findChildViewById(rootView, id);
      if (imgStatus == null) {
        break missingId;
      }

      id = R.id.layoutIcon;
      LinearLayout layoutIcon = ViewBindings.findChildViewById(rootView, id);
      if (layoutIcon == null) {
        break missingId;
      }

      id = R.id.layoutUserInfo;
      LinearLayout layoutUserInfo = ViewBindings.findChildViewById(rootView, id);
      if (layoutUserInfo == null) {
        break missingId;
      }

      id = R.id.phoneListView;
      RecyclerView phoneListView = ViewBindings.findChildViewById(rootView, id);
      if (phoneListView == null) {
        break missingId;
      }

      id = R.id.smsListView;
      RecyclerView smsListView = ViewBindings.findChildViewById(rootView, id);
      if (smsListView == null) {
        break missingId;
      }

      id = R.id.txtUserName;
      TextView txtUserName = ViewBindings.findChildViewById(rootView, id);
      if (txtUserName == null) {
        break missingId;
      }

      return new FragmentMainBinding((FrameLayout) rootView, btnDirectScript, btnTestScript,
          imageView, imgStatus, layoutIcon, layoutUserInfo, phoneListView, smsListView,
          txtUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
