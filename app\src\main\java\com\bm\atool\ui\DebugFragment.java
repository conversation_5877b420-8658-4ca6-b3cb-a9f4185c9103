package com.bm.atool.ui;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.service.SocketService;
import com.bm.atool.service.WatchDogService;
import com.bm.atool.utils.AutoJsExecutor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DebugFragment extends BaseFragment{
    private static final String TAG = "DebugFragment";

    public DebugFragment(){
        setTitle("DEBUG");
    }

    private Button btnStopSocket;
    private Button btnTestSimpleScript;
    private Button btnTestDeviceInfo;
    private Button btnTestAdvancedScript;
    private Button btnTestAccessibilityScript;
    private Button btnTestComplexScript;
    private Button btnStopAllScripts;
    private TextView tvScriptResult;

    private AutoJsExecutor autoJsExecutor;
    private Handler mainHandler;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Initialize view
        View view = inflater.inflate(R.layout.fragment_debug, container, false);

        // Initialize AutoJS executor and handler
        autoJsExecutor = AutoJsExecutor.getInstance(getContext());
        mainHandler = new Handler(Looper.getMainLooper());

        // Initialize UI components
        initViews(view);
        setupClickListeners();

        return view;
    }

    private void initViews(View view) {
        btnStopSocket = view.findViewById(R.id.btnStopSocket);
        btnTestSimpleScript = view.findViewById(R.id.btnTestSimpleScript);
        btnTestDeviceInfo = view.findViewById(R.id.btnTestDeviceInfo);
        btnTestAdvancedScript = view.findViewById(R.id.btnTestAdvancedScript);
        btnTestAccessibilityScript = view.findViewById(R.id.btnTestAccessibilityScript);
        btnTestComplexScript = view.findViewById(R.id.btnTestComplexScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        tvScriptResult = view.findViewById(R.id.tvScriptResult);
    }

    private void setupClickListeners() {
        btnStopSocket.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Stopping all services from DebugFragment");
                Sys.stop();
                Log.d(TAG, "Sys.stop() called.");
            }
        });

        btnTestSimpleScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                executeSimpleScript();
            }
        });

        btnTestDeviceInfo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                executeDeviceInfoScript();
            }
        });

        btnTestAdvancedScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                executeAdvancedScript();
            }
        });

        btnTestAccessibilityScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                executeAccessibilityScript();
            }
        });

        btnTestComplexScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                executeComplexScript();
            }
        });

        btnStopAllScripts.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopAllScripts();
            }
        });
    }

    private void executeSimpleScript() {
        updateResult("执行简单脚本...");
        // 优先尝试从assets加载脚本
        if (autoJsExecutor != null) {
            String executionId = autoJsExecutor.executeScriptFromAssets("simple_test", "scripts/simple_test.js", null);
            if (executionId != null) {
                updateResult("从assets加载简单脚本，执行ID: " + executionId);
                Toast.makeText(getContext(), "简单脚本开始执行", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 如果从assets加载失败，使用内联脚本
        String script = "auto.waitFor();\n" +
                       "console.log('=== 简单脚本开始执行 ===');\n" +
                       "toast('你好啊！！！');\n" +
                       "sleep(1000);\n" +
                       "toast('AutoJS脚本执行成功！');\n" +
                       "console.log('=== 简单脚本执行完成 ===');";

        executeScript("simple_test", script, null);
    }

    private void executeDeviceInfoScript() {
        updateResult("执行设备信息脚本...");
        // 优先尝试从assets加载脚本
        if (autoJsExecutor != null) {
            String executionId = autoJsExecutor.executeScriptFromAssets("device_info_test", "scripts/device_info_test.js", null);
            if (executionId != null) {
                updateResult("从assets加载设备信息脚本，执行ID: " + executionId);
                Toast.makeText(getContext(), "设备信息脚本开始执行", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 如果从assets加载失败，使用内联脚本
        String script = "auto.waitFor();\n" +
                       "console.log('=== 设备信息脚本开始执行 ===');\n" +
                       "var deviceInfo = '设备信息:\\n' +\n" +
                       "    '屏幕尺寸: ' + device.width + 'x' + device.height + '\\n' +\n" +
                       "    'Android版本: ' + device.release + '\\n' +\n" +
                       "    '设备型号: ' + device.model;\n" +
                       "console.log(deviceInfo);\n" +
                       "toast('设备尺寸: ' + device.width + 'x' + device.height);\n" +
                       "sleep(2000);\n" +
                       "toast('Android版本: ' + device.release);\n" +
                       "console.log('=== 设备信息脚本执行完成 ===');";

        executeScript("device_info_test", script, null);
    }

    private void executeAdvancedScript() {
        updateResult("执行高级脚本...");
        // 优先尝试从assets加载脚本
        if (autoJsExecutor != null) {
            String executionId = autoJsExecutor.executeScriptFromAssets("advanced_test", "scripts/advanced_test.js", null);
            if (executionId != null) {
                updateResult("从assets加载高级脚本，执行ID: " + executionId);
                Toast.makeText(getContext(), "高级脚本开始执行", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 如果从assets加载失败，使用简化的内联脚本
        String script = "auto.waitFor();\n" +
                       "console.log('=== 高级脚本开始执行 ===');\n" +
                       "var config = {\n" +
                       "    scriptName: '高级AutoJS测试脚本',\n" +
                       "    version: '1.0.0',\n" +
                       "    author: 'AutoJS用户'\n" +
                       "};\n" +
                       "function log(message) {\n" +
                       "    console.log('[' + new Date().toLocaleTimeString() + '] ' + message);\n" +
                       "}\n" +
                       "log('=== ' + config.scriptName + ' 开始执行 ===');\n" +
                       "toast('你好啊！！！');\n" +
                       "log('显示了基础Toast消息');\n" +
                       "var currentTime = new Date().toLocaleString();\n" +
                       "log('当前时间: ' + currentTime);\n" +
                       "toast('当前时间: ' + currentTime);\n" +
                       "sleep(2000);\n" +
                       "var result = 123 + 456;\n" +
                       "log('计算结果: 123 + 456 = ' + result);\n" +
                       "toast('计算: 123 + 456 = ' + result);\n" +
                       "log('=== 高级脚本执行完成 ===');";

        executeScript("advanced_test", script, null);
    }

    private void executeAccessibilityScript() {
        updateResult("执行无障碍服务脚本...");
        // 优先尝试从assets加载脚本
        if (autoJsExecutor != null) {
            String executionId = autoJsExecutor.executeScriptFromAssets("accessibility_test", "scripts/accessibility_test.js", null);
            if (executionId != null) {
                updateResult("从assets加载无障碍服务脚本，执行ID: " + executionId);
                Toast.makeText(getContext(), "无障碍服务脚本开始执行", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 如果从assets加载失败，使用内联脚本
        String script = "auto.waitFor();\n" +
                       "console.log('=== 无障碍服务脚本开始执行 ===');\n" +
                       "try {\n" +
                       "    var currentApp = currentPackage();\n" +
                       "    console.log('当前应用包名: ' + currentApp);\n" +
                       "    toast('当前应用: ' + currentApp);\n" +
                       "    sleep(2000);\n" +
                       "    var rootNode = getRootInActiveWindow();\n" +
                       "    if (rootNode) {\n" +
                       "        console.log('无障碍服务正常工作');\n" +
                       "        toast('无障碍服务已启用');\n" +
                       "        rootNode.recycle();\n" +
                       "    } else {\n" +
                       "        console.log('无障碍服务可能未正常工作');\n" +
                       "        toast('无障碍服务状态异常');\n" +
                       "    }\n" +
                       "} catch (e) {\n" +
                       "    console.log('检查无障碍服务时出错: ' + e.message);\n" +
                       "    toast('无障碍服务检查失败');\n" +
                       "}\n" +
                       "console.log('=== 无障碍服务脚本执行完成 ===');";

        executeScript("accessibility_test", script, null);
    }

    private void executeComplexScript() {
        updateResult("执行复杂JavaScript脚本...");
        // 优先尝试从assets加载脚本
        if (autoJsExecutor != null) {
            String executionId = autoJsExecutor.executeScriptFromAssets("complex_test", "scripts/complex_test.js", null);
            if (executionId != null) {
                updateResult("从assets加载复杂脚本，执行ID: " + executionId);
                Toast.makeText(getContext(), "复杂脚本开始执行", Toast.LENGTH_SHORT).show();
                return;
            }
        }

        // 如果从assets加载失败，使用简化的内联脚本
        String script = "auto.waitFor();\n" +
                       "console.log('=== 复杂JavaScript脚本开始执行 ===');\n" +
                       "var scriptData = {\n" +
                       "    name: '复杂脚本测试',\n" +
                       "    startTime: new Date().toISOString(),\n" +
                       "    results: {}\n" +
                       "};\n" +
                       "function executeTask(taskName, taskFunction) {\n" +
                       "    try {\n" +
                       "        console.log('执行任务: ' + taskName);\n" +
                       "        var result = taskFunction();\n" +
                       "        scriptData.results[taskName] = { success: true, result: result };\n" +
                       "        console.log('任务完成: ' + taskName);\n" +
                       "        return result;\n" +
                       "    } catch (e) {\n" +
                       "        scriptData.results[taskName] = { success: false, error: e.message };\n" +
                       "        console.log('任务失败: ' + taskName + ', 错误: ' + e.message);\n" +
                       "        return null;\n" +
                       "    }\n" +
                       "}\n" +
                       "executeTask('设备信息', function() {\n" +
                       "    return { width: device.width, height: device.height, model: device.model };\n" +
                       "});\n" +
                       "sleep(1000);\n" +
                       "executeTask('数学计算', function() {\n" +
                       "    var sum = [1, 2, 3, 4, 5].reduce(function(a, b) { return a + b; }, 0);\n" +
                       "    return { sum: sum, average: sum / 5 };\n" +
                       "});\n" +
                       "sleep(1000);\n" +
                       "executeTask('字符串处理', function() {\n" +
                       "    var text = 'Hello AutoJS World';\n" +
                       "    return { original: text, uppercase: text.toUpperCase(), length: text.length };\n" +
                       "});\n" +
                       "scriptData.endTime = new Date().toISOString();\n" +
                       "console.log('脚本执行完成，结果: ' + JSON.stringify(scriptData.results));\n" +
                       "toast('复杂脚本执行完成');\n" +
                       "console.log('=== 复杂JavaScript脚本执行完成 ===');";

        executeScript("complex_test", script, null);
    }

    private void stopAllScripts() {
        updateResult("停止所有脚本...");
        if (autoJsExecutor != null) {
            autoJsExecutor.stopAllScripts();
            updateResult("所有脚本已停止");
            Toast.makeText(getContext(), "所有脚本已停止", Toast.LENGTH_SHORT).show();
        }
    }

    private void executeScript(String scriptId, String script, Map<String, Object> params) {
        if (autoJsExecutor != null) {
            try {
                String executionId = autoJsExecutor.executeScript(scriptId, script, params);
                if (executionId != null) {
                    updateResult("脚本开始执行，ID: " + executionId);
                    Toast.makeText(getContext(), "脚本开始执行", Toast.LENGTH_SHORT).show();
                } else {
                    updateResult("脚本执行失败");
                    Toast.makeText(getContext(), "脚本执行失败", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Log.e(TAG, "执行脚本时出错", e);
                updateResult("脚本执行异常: " + e.getMessage());
                Toast.makeText(getContext(), "脚本执行异常", Toast.LENGTH_SHORT).show();
            }
        } else {
            updateResult("AutoJS执行器未初始化");
            Toast.makeText(getContext(), "AutoJS执行器未初始化", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateResult(String message) {
        if (mainHandler != null && tvScriptResult != null) {
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    String currentTime = new java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date());
                    String newText = "[" + currentTime + "] " + message + "\n" + tvScriptResult.getText().toString();
                    if (newText.length() > 2000) {
                        newText = newText.substring(0, 2000) + "...";
                    }
                    tvScriptResult.setText(newText);
                }
            });
        }
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_debug;
    }
}
