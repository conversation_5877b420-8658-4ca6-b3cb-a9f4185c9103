/**
 * AutoJS集成测试脚本
 * 用于验证ATool项目中的AutoJS功能是否正常工作
 * 
 * 使用方法：
 * 1. 确保ATool应用已安装并启用无障碍服务
 * 2. 打开DebugFragment界面
 * 3. 点击相应的测试按钮
 * 4. 观察Toast消息和日志输出
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== ATool AutoJS集成测试开始 ===");

// 测试基础功能
function testBasicFunctions() {
    console.log("1. 测试基础功能");
    
    // 测试Toast
    toast("AutoJS集成测试开始");
    sleep(1000);
    
    // 测试设备信息
    console.log("设备宽度: " + device.width);
    console.log("设备高度: " + device.height);
    console.log("设备型号: " + device.model);
    console.log("Android版本: " + device.release);
    
    toast("设备: " + device.model + " (" + device.width + "x" + device.height + ")");
    sleep(2000);
}

// 测试无障碍服务
function testAccessibilityService() {
    console.log("2. 测试无障碍服务");
    
    try {
        // 获取当前应用包名
        var packageName = currentPackage();
        console.log("当前应用包名: " + packageName);
        toast("当前应用: " + packageName);
        sleep(2000);
        
        // 获取根节点
        var rootNode = getRootInActiveWindow();
        if (rootNode) {
            console.log("无障碍服务正常，根节点类名: " + rootNode.className());
            toast("无障碍服务正常工作");
            rootNode.recycle();
        } else {
            console.log("无障碍服务异常：无法获取根节点");
            toast("无障碍服务异常");
        }
        sleep(2000);
        
    } catch (e) {
        console.log("无障碍服务测试失败: " + e.message);
        toast("无障碍服务测试失败");
    }
}

// 测试JavaScript功能
function testJavaScriptFeatures() {
    console.log("3. 测试JavaScript功能");
    
    // 测试数组操作
    var numbers = [1, 2, 3, 4, 5];
    var sum = numbers.reduce(function(a, b) { return a + b; }, 0);
    console.log("数组求和: " + sum);
    
    // 测试对象操作
    var testObj = {
        name: "AutoJS测试",
        version: "1.0.0",
        timestamp: new Date().toISOString()
    };
    console.log("测试对象: " + JSON.stringify(testObj));
    
    // 测试字符串操作
    var testString = "Hello AutoJS World";
    console.log("原字符串: " + testString);
    console.log("大写: " + testString.toUpperCase());
    console.log("长度: " + testString.length);
    
    toast("JavaScript功能测试完成");
    sleep(2000);
}

// 测试时间和日期
function testDateTime() {
    console.log("4. 测试时间和日期功能");
    
    var now = new Date();
    var timeInfo = {
        timestamp: now.getTime(),
        iso: now.toISOString(),
        locale: now.toLocaleString(),
        year: now.getFullYear(),
        month: now.getMonth() + 1,
        day: now.getDate(),
        hour: now.getHours(),
        minute: now.getMinutes()
    };
    
    console.log("当前时间信息: " + JSON.stringify(timeInfo));
    toast("当前时间: " + timeInfo.locale);
    sleep(2000);
}

// 测试错误处理
function testErrorHandling() {
    console.log("5. 测试错误处理");
    
    try {
        // 故意触发一个错误
        var undefinedVar = someUndefinedFunction();
    } catch (e) {
        console.log("捕获到预期错误: " + e.message);
        toast("错误处理测试通过");
    }
    
    sleep(2000);
}

// 主测试函数
function runIntegrationTest() {
    console.log("开始ATool AutoJS集成测试");
    
    try {
        testBasicFunctions();
        testAccessibilityService();
        testJavaScriptFeatures();
        testDateTime();
        testErrorHandling();
        
        console.log("=== 所有测试完成 ===");
        toast("AutoJS集成测试全部通过！");
        
        // 生成测试报告
        var report = {
            testName: "ATool AutoJS集成测试",
            timestamp: new Date().toISOString(),
            device: {
                model: device.model,
                release: device.release,
                width: device.width,
                height: device.height
            },
            status: "SUCCESS",
            message: "所有测试项目均通过"
        };
        
        console.log("测试报告: " + JSON.stringify(report, null, 2));
        
    } catch (e) {
        console.log("测试过程中发生错误: " + e.message);
        toast("测试失败: " + e.message);
        
        var errorReport = {
            testName: "ATool AutoJS集成测试",
            timestamp: new Date().toISOString(),
            status: "FAILED",
            error: e.message
        };
        
        console.log("错误报告: " + JSON.stringify(errorReport, null, 2));
    }
}

// 执行测试
runIntegrationTest();
