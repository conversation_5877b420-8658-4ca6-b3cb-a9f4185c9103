# ATool AutoJS脚本测试说明

## 概述

本项目已集成AutoJS功能，可以通过DebugFragment界面测试各种JavaScript脚本的执行。

## 功能特性

### 1. 脚本执行引擎
- 基于AutoJS Pro v1.0.1
- 支持完整的JavaScript语法
- 集成无障碍服务API
- 支持异步脚本执行
- 实时执行结果反馈

### 2. 测试脚本类型

#### 简单脚本测试 (simple_test.js)
- **功能**: 基础Toast消息显示
- **测试内容**: 
  - 无障碍服务权限检查
  - Toast消息显示
  - 基础日志输出
  - 设备信息获取

#### 设备信息脚本 (device_info_test.js)
- **功能**: 获取并显示设备详细信息
- **测试内容**:
  - 屏幕尺寸和分辨率
  - Android版本信息
  - 设备型号和品牌
  - 屏幕方向检测
  - SDK版本信息

#### 高级脚本测试 (advanced_test.js)
- **功能**: 演示AutoJS高级功能
- **测试内容**:
  - 复杂函数定义和调用
  - 对象和数组操作
  - 时间处理和格式化
  - 数学计算和算法
  - 性能测试
  - 错误处理机制

#### 无障碍服务脚本 (accessibility_test.js)
- **功能**: 测试无障碍服务功能
- **测试内容**:
  - 当前应用包名获取
  - 根节点访问
  - 窗口信息获取
  - 屏幕状态检查
  - 节点遍历和操作

#### 复杂JavaScript脚本 (complex_test.js)
- **功能**: 演示复杂的JavaScript功能
- **测试内容**:
  - 任务管理系统
  - 动态函数执行
  - JSON数据处理
  - 统计和报告生成
  - 多任务协调执行

## 使用方法

### 1. 启用无障碍服务
在测试脚本之前，请确保已启用ATool的无障碍服务：
1. 打开手机设置
2. 找到"无障碍"或"辅助功能"
3. 启用"ATool"服务
4. 确认权限授予

### 2. 执行测试脚本
1. 打开ATool应用
2. 切换到"DEBUG"标签页
3. 点击相应的测试按钮：
   - "测试简单脚本" - 执行基础功能测试
   - "测试设备信息脚本" - 获取设备信息
   - "测试高级脚本" - 演示高级功能
   - "测试无障碍服务脚本" - 测试无障碍API
   - "测试复杂JavaScript脚本" - 执行复杂逻辑

### 3. 查看执行结果
- 脚本执行状态会在界面底部的文本区域显示
- Toast消息会在屏幕上弹出显示
- 详细日志可通过Logcat查看（标签：AutoJsExecutor）

### 4. 停止脚本执行
- 点击"停止所有脚本"按钮可以终止当前运行的所有脚本
- 单个脚本也会在执行完成后自动停止

## 脚本开发指南

### 1. 基础结构
```javascript
// 请求无障碍权限
auto.waitFor();

// 脚本主体
console.log("脚本开始执行");
toast("Hello AutoJS");

// 脚本结束
console.log("脚本执行完成");
```

### 2. 常用API
- `auto.waitFor()` - 等待无障碍服务就绪
- `toast(message)` - 显示Toast消息
- `sleep(ms)` - 暂停执行
- `console.log(message)` - 输出日志
- `device.width/height` - 获取屏幕尺寸
- `currentPackage()` - 获取当前应用包名
- `getRootInActiveWindow()` - 获取根节点

### 3. 错误处理
```javascript
try {
    // 可能出错的代码
    var result = someFunction();
} catch (e) {
    console.log("错误: " + e.message);
    toast("执行失败");
}
```

## 故障排除

### 常见问题

1. **脚本执行失败**
   - 检查无障碍服务是否启用
   - 确认脚本语法正确
   - 查看Logcat错误信息

2. **Toast不显示**
   - 检查通知权限
   - 确认脚本正常执行
   - 尝试重启应用

3. **无障碍功能异常**
   - 重新启用无障碍服务
   - 检查系统权限设置
   - 重启设备

### 调试技巧

1. **使用console.log输出调试信息**
2. **分段测试复杂脚本**
3. **检查Logcat日志**
4. **使用try-catch捕获异常**

## 技术架构

### 核心组件
- **AutoJsExecutor**: 脚本执行引擎
- **DebugFragment**: 测试界面
- **ANTAccessibilityService**: 无障碍服务
- **ScriptExecution**: 脚本执行管理

### 执行流程
1. 用户点击测试按钮
2. DebugFragment调用AutoJsExecutor
3. AutoJsExecutor初始化脚本环境
4. 执行JavaScript代码
5. 返回执行结果和状态

## 扩展开发

如需添加新的测试脚本：
1. 在`app/src/main/assets/scripts/`目录下创建新的.js文件
2. 在DebugFragment中添加对应的按钮和执行方法
3. 使用`autoJsExecutor.executeScriptFromAssets()`加载执行

## 注意事项

1. **权限要求**: 脚本执行需要无障碍服务权限
2. **性能影响**: 复杂脚本可能影响设备性能
3. **兼容性**: 不同Android版本可能有API差异
4. **安全性**: 避免执行不可信的脚本代码
