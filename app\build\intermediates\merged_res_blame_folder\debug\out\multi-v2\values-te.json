{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3121,3223,3331,3433,3534,3640,3747,8938", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3218,3326,3428,3529,3635,3742,3866,9034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "281,398,510,623,713,818,937,1015,1091,1182,1275,1370,1464,1564,1657,1752,1847,1938,2029,2118,2232,2336,2435,2550,2655,2770,2932,8855", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "393,505,618,708,813,932,1010,1086,1177,1270,1365,1459,1559,1652,1747,1842,1933,2024,2113,2227,2331,2430,2545,2650,2765,2927,3030,8933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,317,421,537,626,692,786,853,915,1008,1076,1139,1213,1278,1332,1453,1510,1572,1626,1705,1833,1921,2013,2128,2208,2290,2378,2445,2511,2586,2664,2754,2827,2903,2984,3053,3158,3235,3326,3419,3493,3570,3662,3717,3783,3867,3953,4016,4081,4145,4255,4367,4466,4585", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "226,312,416,532,621,687,781,848,910,1003,1071,1134,1208,1273,1327,1448,1505,1567,1621,1700,1828,1916,2008,2123,2203,2285,2373,2440,2506,2581,2659,2749,2822,2898,2979,3048,3153,3230,3321,3414,3488,3565,3657,3712,3778,3862,3948,4011,4076,4140,4250,4362,4461,4580,4663"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3035,3871,3975,4091,4253,4418,4512,4579,4641,4734,4802,4865,4939,5004,5058,5179,5236,5298,5352,5431,5559,5647,5739,5854,5934,6016,6104,6171,6237,6312,6390,6480,6553,6629,6710,6779,6884,6961,7052,7145,7219,7296,7388,7443,7509,7593,7679,7742,7807,7871,7981,8093,8192,8626", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "276,3116,3970,4086,4175,4314,4507,4574,4636,4729,4797,4860,4934,4999,5053,5174,5231,5293,5347,5426,5554,5642,5734,5849,5929,6011,6099,6166,6232,6307,6385,6475,6548,6624,6705,6774,6879,6956,7047,7140,7214,7291,7383,7438,7504,7588,7674,7737,7802,7866,7976,8088,8187,8306,8704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4180,4319,8547,8709,9039,9208,9295", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "4248,4413,8621,8850,9203,9290,9374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,122", "endOffsets": "163,286"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8311,8424", "endColumns": "112,122", "endOffsets": "8419,8542"}}]}]}