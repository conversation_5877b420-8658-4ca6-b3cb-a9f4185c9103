/**
 * AutoJS高级测试脚本
 * 基于你的Android项目代码结构设计
 * 包含多种AutoJS功能演示
 */

// 请求无障碍权限
auto.waitFor();

// 脚本配置
var config = {
    scriptName: "高级AutoJS测试脚本",
    version: "1.0.0",
    author: "AutoJS用户"
};

// 日志函数
function log(message) {
    console.log("[" + new Date().toLocaleTimeString() + "] " + message);
}

// 显示Toast的封装函数
function showToast(message, duration) {
    duration = duration || "short";
    if (duration === "long") {
        toast(message);
        sleep(3000);
    } else {
        toast(message);
        sleep(1500);
    }
}

// 主函数
function main() {
    log("=== " + config.scriptName + " 开始执行 ===");
    
    // 1. 基础Toast演示
    showToast("你好啊！！！");
    log("显示了基础Toast消息");
    
    // 2. 显示设备信息
    var deviceInfo = "设备信息:\n" +
                    "屏幕尺寸: " + device.width + "x" + device.height + "\n" +
                    "Android版本: " + device.release + "\n" +
                    "设备型号: " + device.model;
    
    log(deviceInfo);
    showToast("设备尺寸: " + device.width + "x" + device.height);
    
    // 3. 获取当前应用包名
    var currentApp = currentPackage();
    log("当前应用包名: " + currentApp);
    showToast("当前应用: " + currentApp);
    
    // 4. 时间演示
    var currentTime = new Date().toLocaleString();
    log("当前时间: " + currentTime);
    showToast("当前时间: " + currentTime, "long");
    
    // 5. 简单的计算演示
    var result = 123 + 456;
    log("计算结果: 123 + 456 = " + result);
    showToast("计算: 123 + 456 = " + result);
    
    // 6. 检查无障碍服务状态
    try {
        // 尝试获取屏幕上的节点来测试无障碍服务
        var rootNode = getRootInActiveWindow();
        if (rootNode) {
            log("无障碍服务正常工作");
            showToast("无障碍服务已启用");
            rootNode.recycle();
        } else {
            log("无障碍服务可能未正常工作");
            showToast("无障碍服务状态异常");
        }
    } catch (e) {
        log("检查无障碍服务时出错: " + e.message);
        showToast("无障碍服务检查失败");
    }
    
    // 7. 最终完成提示
    showToast("🎉 所有测试完成！", "long");
    log("=== " + config.scriptName + " 执行完成 ===");
}

// 错误处理
try {
    main();
} catch (error) {
    log("脚本执行出错: " + error.message);
    toast("脚本执行出错: " + error.message);
} finally {
    log("脚本资源清理完成");
    exit();
}
