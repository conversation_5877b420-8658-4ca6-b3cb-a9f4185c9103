<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_background_color"
    tools:context=".ui.MainFragment">
    <LinearLayout
        android:orientation="vertical"
        android:layout_height="match_parent"
        android:layout_width="match_parent">

        <LinearLayout
            android:id="@+id/layoutIcon"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/header_background"
            android:gravity="left|center_vertical"
            android:orientation="horizontal"
            android:padding="8dp"
            android:elevation="4dp">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="0dp"
                android:src="@mipmap/ic_launcher"
                app:srcCompat="@mipmap/ic_launcher_round" />

            <TextView
                android:id="@+id/txtUserName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="username"
                android:textAlignment="viewStart"
                android:textSize="20sp"
                android:textColor="@color/text_primary"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"/>

            <ImageView
                android:id="@+id/imgStatus"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="5dp"
                android:src="@android:drawable/presence_online" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layoutUserInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:background="@color/surface_background"
            android:elevation="2dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/phoneListView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <!-- 测试按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:background="@color/surface_background"
            android:gravity="center"
            android:padding="8dp"
            android:elevation="2dp">

            <Button
                android:id="@+id/btnTestScript"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="模拟脚本执行"
                android:background="@color/header_background"
                android:textColor="@android:color/white"
                android:padding="8dp" />

            <Button
                android:id="@+id/btnDirectScript"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="直接执行脚本"
                android:background="@color/header_background"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp"
                android:padding="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingTop="8dp"
            android:layout_margin="8dp"
            android:background="@color/surface_background"
            android:elevation="2dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:text="SMS:"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/smsListView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
