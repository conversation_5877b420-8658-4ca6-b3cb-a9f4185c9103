{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,240,323,422,535,615,685,775,845,905,992,1057,1118,1182,1243,1297,1398,1459,1519,1573,1643,1754,1841,1922,2035,2114,2196,2288,2355,2421,2491,2569,2655,2727,2805,2874,2943,3025,3113,3206,3300,3374,3443,3538,3590,3658,3743,3831,3893,3957,4020,4120,4213,4310,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "235,318,417,530,610,680,770,840,900,987,1052,1113,1177,1238,1292,1393,1454,1514,1568,1638,1749,1836,1917,2030,2109,2191,2283,2350,2416,2486,2564,2650,2722,2800,2869,2938,3020,3108,3201,3295,3369,3438,3533,3585,3653,3738,3826,3888,3952,4015,4115,4208,4305,4398,4475"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2919,3717,3816,3929,4079,4235,4325,4395,4455,4542,4607,4668,4732,4793,4847,4948,5009,5069,5123,5193,5304,5391,5472,5585,5664,5746,5838,5905,5971,6041,6119,6205,6277,6355,6424,6493,6575,6663,6756,6850,6924,6993,7088,7140,7208,7293,7381,7443,7507,7570,7670,7763,7860,8251", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "285,2997,3811,3924,4004,4144,4320,4390,4450,4537,4602,4663,4727,4788,4842,4943,5004,5064,5118,5188,5299,5386,5467,5580,5659,5741,5833,5900,5966,6036,6114,6200,6272,6350,6419,6488,6570,6658,6751,6845,6919,6988,7083,7135,7203,7288,7376,7438,7502,7565,7665,7758,7855,7948,8323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4009,4149,8171,8328,8650,8818,8898", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "4074,4230,8246,8462,8813,8893,8971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "290,395,488,596,681,783,893,971,1048,1139,1232,1323,1417,1517,1610,1705,1799,1890,1981,2062,2165,2263,2361,2464,2570,2671,2824,8467", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "390,483,591,676,778,888,966,1043,1134,1227,1318,1412,1512,1605,1700,1794,1885,1976,2057,2160,2258,2356,2459,2565,2666,2819,2914,8544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3002,3098,3201,3299,3397,3500,3605,8549", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3093,3196,3294,3392,3495,3600,3712,8645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "7953,8057", "endColumns": "103,113", "endOffsets": "8052,8166"}}]}]}