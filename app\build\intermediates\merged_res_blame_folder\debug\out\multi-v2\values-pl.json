{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "36,37,38,39,40,41,42,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3182,3279,3381,3479,3578,3692,3797,8828", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3274,3376,3474,3573,3687,3792,3914,8924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "46,48,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4203,4337,8455,8611,8929,9098,9179", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "4268,4423,8530,8740,9093,9174,9251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,116", "endOffsets": "158,275"}, "to": {"startLines": "97,98", "startColumns": "4,4", "startOffsets": "8230,8338", "endColumns": "107,116", "endOffsets": "8333,8450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,341,420,505,622,704,768,849,913,974,1085,1153,1207,1276,1338,1392,1503,1564,1626,1680,1752,1881,1970,2052,2171,2253,2336,2423,2490,2556,2627,2703,2792,2869,2947,3025,3101,3191,3264,3359,3456,3528,3602,3702,3754,3820,3908,3998,4060,4124,4187,4294,4383,4482,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "336,415,500,617,699,763,844,908,969,1080,1148,1202,1271,1333,1387,1498,1559,1621,1675,1747,1876,1965,2047,2166,2248,2331,2418,2485,2551,2622,2698,2787,2864,2942,3020,3096,3186,3259,3354,3451,3523,3597,3697,3749,3815,3903,3993,4055,4119,4182,4289,4378,4477,4565,4641"}, "to": {"startLines": "2,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3103,3919,4004,4121,4273,4428,4509,4573,4634,4745,4813,4867,4936,4998,5052,5163,5224,5286,5340,5412,5541,5630,5712,5831,5913,5996,6083,6150,6216,6287,6363,6452,6529,6607,6685,6761,6851,6924,7019,7116,7188,7262,7362,7414,7480,7568,7658,7720,7784,7847,7954,8043,8142,8535", "endLines": "7,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "386,3177,3999,4116,4198,4332,4504,4568,4629,4740,4808,4862,4931,4993,5047,5158,5219,5281,5335,5407,5536,5625,5707,5826,5908,5991,6078,6145,6211,6282,6358,6447,6524,6602,6680,6756,6846,6919,7014,7111,7183,7257,7357,7409,7475,7563,7653,7715,7779,7842,7949,8038,8137,8225,8606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "391,506,608,716,802,909,1028,1107,1183,1274,1367,1462,1556,1657,1750,1845,1940,2031,2122,2204,2313,2413,2512,2621,2733,2844,3007,8745", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "501,603,711,797,904,1023,1102,1178,1269,1362,1457,1551,1652,1745,1840,1935,2026,2117,2199,2308,2408,2507,2616,2728,2839,3002,3098,8823"}}]}]}