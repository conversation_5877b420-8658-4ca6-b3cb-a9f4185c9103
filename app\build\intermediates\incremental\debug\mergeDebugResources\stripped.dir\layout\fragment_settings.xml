<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_background_color"
    tools:context=".ui.SettingsFragment">

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:scrollbars="vertical"
        android:fadingEdge="vertical"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Permissions"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:padding="8dp"
                android:background="@color/header_background"
                android:elevation="4dp"
                android:layout_marginBottom="8dp"/>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="2dp"
                android:layout_marginBottom="16dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/permissionsListView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/surface_background"
                    android:padding="8dp"/>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnLogout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/surface_background"
                    android:textColor="@color/text_primary"
                    android:layout_marginLeft="3dp"
                    android:layout_marginRight="3dp"
                    android:elevation="4dp"
                    android:text="Logout" />

                <Button
                    android:id="@+id/btnExit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/surface_background"
                    android:textColor="@color/text_primary"
                    android:layout_marginLeft="3dp"
                    android:layout_marginRight="3dp"
                    android:elevation="4dp"
                    android:text="Exit" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</FrameLayout>
