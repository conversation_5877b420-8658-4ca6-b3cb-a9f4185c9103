<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_main_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="13"/></Target><Target id="@+id/layoutIcon" view="LinearLayout"><Expressions/><location startLine="14" startOffset="8" endLine="52" endOffset="22"/></Target><Target id="@+id/imageView" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="31" endOffset="59"/></Target><Target id="@+id/txtUserName" view="TextView"><Expressions/><location startLine="33" startOffset="12" endLine="43" endOffset="80"/></Target><Target id="@+id/imgStatus" view="ImageView"><Expressions/><location startLine="45" startOffset="12" endLine="51" endOffset="65"/></Target><Target id="@+id/layoutUserInfo" view="LinearLayout"><Expressions/><location startLine="54" startOffset="8" endLine="71" endOffset="22"/></Target><Target id="@+id/phoneListView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="67" startOffset="12" endLine="70" endOffset="54"/></Target><Target id="@+id/btnTestScript" view="Button"><Expressions/><location startLine="86" startOffset="12" endLine="93" endOffset="39"/></Target><Target id="@+id/btnDirectScript" view="Button"><Expressions/><location startLine="95" startOffset="12" endLine="103" endOffset="39"/></Target><Target id="@+id/smsListView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="127" startOffset="12" endLine="130" endOffset="54"/></Target></Targets></Layout>