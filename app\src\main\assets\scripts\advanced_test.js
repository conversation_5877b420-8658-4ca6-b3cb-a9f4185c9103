/**
 * 高级AutoJS测试脚本
 * 功能：演示AutoJS的高级功能和API
 */

// 请求无障碍权限
auto.waitFor();

console.log("=== 高级AutoJS脚本开始执行 ===");

// 脚本配置
var config = {
    scriptName: "高级AutoJS测试脚本",
    version: "1.0.0",
    author: "AutoJS用户",
    features: [
        "Toast消息显示",
        "设备信息获取",
        "时间处理",
        "数学计算",
        "字符串操作",
        "无障碍服务测试"
    ]
};

// 工具函数库
var AdvancedUtils = {
    // 增强的日志函数
    log: function(message, level) {
        level = level || "INFO";
        var timestamp = new Date().toLocaleTimeString();
        var logMessage = "[" + timestamp + "] [" + level + "] " + message;
        console.log(logMessage);
        return logMessage;
    },
    
    // 增强的Toast显示函数
    showToast: function(message, duration, callback) {
        duration = duration || "short";
        this.log("显示Toast: " + message);
        
        toast(message);
        
        var sleepTime = duration === "long" ? 3000 : 1500;
        sleep(sleepTime);
        
        if (callback && typeof callback === 'function') {
            callback();
        }
    },
    
    // 设备信息获取
    getDeviceInfo: function() {
        try {
            var info = {
                basic: {
                    width: device.width,
                    height: device.height,
                    model: device.model,
                    release: device.release,
                    sdk: device.sdkInt
                },
                advanced: {
                    brand: device.brand || "未知",
                    product: device.product || "未知",
                    isScreenOn: device.isScreenOn(),
                    currentApp: currentPackage()
                },
                calculated: {
                    aspectRatio: (device.width / device.height).toFixed(2),
                    totalPixels: device.width * device.height,
                    diagonal: Math.sqrt(Math.pow(device.width, 2) + Math.pow(device.height, 2)).toFixed(2)
                }
            };
            
            this.log("设备信息获取成功");
            return info;
        } catch (e) {
            this.log("设备信息获取失败: " + e.message, "ERROR");
            return null;
        }
    },
    
    // 性能测试
    performanceTest: function() {
        this.log("开始性能测试");
        
        var tests = {
            mathCalculation: this.testMathPerformance(),
            stringOperation: this.testStringPerformance(),
            arrayOperation: this.testArrayPerformance()
        };
        
        this.log("性能测试完成");
        return tests;
    },
    
    testMathPerformance: function() {
        var startTime = Date.now();
        var result = 0;
        
        for (var i = 0; i < 10000; i++) {
            result += Math.sqrt(i) * Math.sin(i) + Math.cos(i);
        }
        
        var endTime = Date.now();
        return {
            operation: "数学计算",
            iterations: 10000,
            result: result.toFixed(2),
            duration: endTime - startTime
        };
    },
    
    testStringPerformance: function() {
        var startTime = Date.now();
        var str = "";
        
        for (var i = 0; i < 1000; i++) {
            str += "AutoJS测试字符串" + i + " ";
        }
        
        var endTime = Date.now();
        return {
            operation: "字符串拼接",
            iterations: 1000,
            finalLength: str.length,
            duration: endTime - startTime
        };
    },
    
    testArrayPerformance: function() {
        var startTime = Date.now();
        var arr = [];
        
        for (var i = 0; i < 5000; i++) {
            arr.push(i);
        }
        
        var sum = arr.reduce(function(a, b) { return a + b; }, 0);
        var endTime = Date.now();
        
        return {
            operation: "数组操作",
            arrayLength: arr.length,
            sum: sum,
            duration: endTime - startTime
        };
    }
};

// 主执行流程
function main() {
    AdvancedUtils.log("=== " + config.scriptName + " 开始执行 ===");
    
    // 1. 显示脚本信息
    AdvancedUtils.showToast("你好啊！！！");
    AdvancedUtils.log("显示了基础Toast消息");
    
    // 2. 获取并显示设备信息
    var deviceInfo = AdvancedUtils.getDeviceInfo();
    if (deviceInfo) {
        var infoText = "设备: " + deviceInfo.basic.model + 
                      " | 尺寸: " + deviceInfo.basic.width + "x" + deviceInfo.basic.height +
                      " | 比例: " + deviceInfo.calculated.aspectRatio;
        
        AdvancedUtils.log("设备信息: " + JSON.stringify(deviceInfo.basic));
        AdvancedUtils.showToast(infoText, "long");
    }
    
    // 3. 时间演示
    var currentTime = new Date();
    var timeInfo = {
        iso: currentTime.toISOString(),
        locale: currentTime.toLocaleString(),
        timestamp: currentTime.getTime(),
        formatted: currentTime.getFullYear() + "年" + 
                  (currentTime.getMonth() + 1) + "月" + 
                  currentTime.getDate() + "日 " + 
                  currentTime.getHours() + ":" + 
                  String(currentTime.getMinutes()).padStart(2, '0')
    };
    
    AdvancedUtils.log("当前时间: " + timeInfo.formatted);
    AdvancedUtils.showToast("当前时间: " + timeInfo.formatted, "long");
    
    // 4. 数学计算演示
    var mathResults = {
        simple: 123 + 456,
        complex: Math.PI * Math.pow(2, 3),
        fibonacci: generateFibonacci(8),
        prime: findPrimes(20)
    };
    
    AdvancedUtils.log("数学计算结果: " + JSON.stringify(mathResults));
    AdvancedUtils.showToast("计算: 123 + 456 = " + mathResults.simple);
    
    // 5. 性能测试
    var performanceResults = AdvancedUtils.performanceTest();
    AdvancedUtils.log("性能测试结果: " + JSON.stringify(performanceResults));
    
    var avgDuration = Object.values(performanceResults)
        .reduce(function(sum, test) { return sum + test.duration; }, 0) / 3;
    AdvancedUtils.showToast("性能测试完成，平均耗时: " + avgDuration.toFixed(2) + "ms");
    
    // 6. 无障碍服务测试
    try {
        var rootNode = getRootInActiveWindow();
        if (rootNode) {
            AdvancedUtils.log("无障碍服务正常工作");
            AdvancedUtils.showToast("无障碍服务已启用");
            rootNode.recycle();
        } else {
            AdvancedUtils.log("无障碍服务可能未正常工作", "WARN");
            AdvancedUtils.showToast("无障碍服务状态异常");
        }
    } catch (e) {
        AdvancedUtils.log("检查无障碍服务时出错: " + e.message, "ERROR");
        AdvancedUtils.showToast("无障碍服务检查失败");
    }
    
    AdvancedUtils.log("=== " + config.scriptName + " 执行完成 ===");
}

// 辅助函数
function generateFibonacci(n) {
    var fib = [0, 1];
    for (var i = 2; i < n; i++) {
        fib[i] = fib[i-1] + fib[i-2];
    }
    return fib;
}

function findPrimes(max) {
    var primes = [];
    for (var i = 2; i <= max; i++) {
        var isPrime = true;
        for (var j = 2; j < i; j++) {
            if (i % j === 0) {
                isPrime = false;
                break;
            }
        }
        if (isPrime) {
            primes.push(i);
        }
    }
    return primes;
}

// 执行主函数
main();
