// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.safeParcelVersion = '1.7.0'

    ext.kotlinVersion = '1.7.1'

    ext.wireVersion = '4.9.2'

//    ext.androidBuildGradleVersion = '3.6.3'

//    ext.androidBuildVersionTools = '29.0.3'

//    ext.androidMinSdk = 9
//    ext.androidTargetSdk = 29
//    ext.androidCompileSdk = 30

//    repositories {
//        jcenter()
//        google()
//    }

    dependencies {
//        classpath "com.android.tools.build:gradle:$androidBuildGradleVersion"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath "com.squareup.wire:wire-gradle-plugin:$wireVersion"

//        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.20'
//        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.9.4'

        classpath 'com.github.megatronking.stringfog:gradle-plugin:5.2.0'
        // 选用加解密算法库，默认实现了xor算法，也可以使用自己的加解密库。
        classpath 'com.github.megatronking.stringfog:xor:5.0.0'
    }
}

plugins {
    id 'com.android.application' version '8.2.0' apply false
    id 'com.android.library' version '8.2.0' apply false
}

allprojects {
    repositories {
        maven { url 'https://jitpack.io' }
    }
}